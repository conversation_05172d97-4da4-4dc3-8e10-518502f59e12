<?php
/**
 * Prenotazioni Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once '../includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Masseria Torricella',
    'page_description' => '',
    'page_keywords' => '',
    'current_page' => 'prenotazioni.htm'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

<div class="page-title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h3>BOOKING</h3>
          </div>
        </div>
      </div>
    </div>
    <!-- Contact Us -->
        <div class="contact-us container">
            <div class="row">
                <div class="services-half-width-text span6">
                   
                 <table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="padding:15px;">
          <tr>
            <td>
              <form action="http://www.masseriatorricella.it/prenotacdosys.asp" method="post" style="margin:0px;" onsubmit="MM_validateForm('Arrivo','','R','Partenza','','R','camere','','RisNum','email','','RisEmail');return document.MM_returnValue">
              <div align="left">
                <h4><strong>On line availability and/or reservation request
<br />  
                </strong></h4>
              </div>
            <br />
             <div style="background-color:#E5E0A6;padding:20px; text-align:left;">
               <table width="100%" border="0" cellspacing="5" cellpadding="0">
                 <tr>
                   <td width="50%"><strong>Check-in*:</strong></td>
                   <td><input name="Arrivo" id="Arrivo" onclick='scwShow(this,event);' /></td>
                 </tr>
                 <tr>
                   <td><strong>Check-out*:</strong></td>
                   <td><input name="Partenza" id="Partenza" onclick='scwShow(this,event);' />                   </td>
                 </tr>
                 <tr>
                   <td>&nbsp;</td>
                   <td>&nbsp;</td>
                 </tr>
                 <tr>
                   <td>&nbsp;</td>
                   <td>&nbsp;</td>
                 </tr>
                 <tr>
                   <td><strong>Adults:</strong></td>
                   <td><input name="Adulti" type="text" id="Adulti" size="5" /></td>
                 </tr>
                 <tr>
                   <td><strong>Children (from 3 to 10 years):</strong></td>
                   <td><input name="Bambini" type="text" id="Bambini" size="5" /></td>
                 </tr>
                 <tr>
                   <td valign="top"><strong>Number of bedrooms*:</strong></td>
                   <td><input name="camere" type="text" id="camere" size="5" />
                     <br />
                     <br /></td>
                 </tr>
                 <tr>
                   <td colspan="2"><table width="100%">
                       <tr>
                         <td width="26%"><strong>
                           <label>
                           <input name="Scelta" type="radio" id="RadioGroup1_0" value="BB" checked="checked" />
                           bed and breakfast</label>
                         </strong></td>
                         <td width="24%"><strong>
                           <label>
                           <input type="radio" name="Scelta" value="MP" id="RadioGroup1_1" />
                           Half Board </label>
                         </strong></td>
                         <td width="24%"><strong>
                           <label>
                           <input type="radio" name="Scelta" value="PC" id="RadioGroup1_2" />
                           Full Board</label>
                            </strong></td>
                            <td width="26%"><strong>
                           <label>
                           <input type="radio" name="Scelta" value="PC" id="RadioGroup1_3" />
                           HOLIDAY HOMES  </label>
                         </strong></td>
                        </tr>
                       
                       </table></td>
                  </tr>
                 <tr>
                   <td colspan="2"><strong><br />
                    Special requests:<br />
                   </strong><textarea style="width:97%" name="Richieste" id="Richieste" cols="30" rows="5"></textarea></td>
                  </tr>
               </table>
             </div>
             <div align="left"><br />
               <strong>Your personal information</strong> </div>
             <div style="background-color:#E5E0A6;padding:20px; text-align:left;">
               <table width="100%" border="0" cellspacing="5" cellpadding="0">
                  <tr>
                    <td width="50%"><strong>First name</strong></td>
                    <td><input name="Nome" type="text" id="Nome" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Surname</strong></td>
                    <td><input name="Cognome" type="text" id="Cognome" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Address</strong></td>
                    <td><input name="Indirizzo" type="text" id="Indirizzo" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Postal Code</strong></td>
                    <td><input name="CAP" type="text" id="CAP" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>City</strong></td>
                    <td><input name="Città" type="text" id="Città" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Phone</strong></td>
                    <td><input name="Telefono" type="text" id="Telefono" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Fax</strong></td>
                    <td><input name="Fax" type="text" id="Fax" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>E-mail*</strong></td>
                    <td><input name="email" type="text" id="email" style="width:97%" /></td>
                  </tr>
                </table>
             </div>
             
             <div align="left"><strong><br />
               Tutela della privacy:</strong><br />
               ai sensi della legge 196/03 sulla tutela dei dati personali, i dati inviati
               saranno utilizzati esclusivamente per attività legate ai servizi offerti dall'Azienda Agrituristica Masseria Torricella e non saranno per alcuna 
               ragione ceduti a terzi.
             </div>
             <table width="51%" border="0" cellspacing="5" cellpadding="0">
              <tr>
                <td width="26%" valign="top"><div align="left">
                        <strong>Autorizzo</strong>
                  <input name="check_privacy" type="checkbox" id="check_privacy" value="autorizzo_privacy" />
                  </td>
                <td width="51%"><div align="center"><br>
                  <input type="submit" name="button" id="button" value="Invia&gt;&gt;" />
                </div></td>
              </tr>
            </table>
             </form>             </td>
          </tr>
          
          <tr>
            <td>&nbsp;</td>
          </tr>
        </table>
              </div>
                <div class="services-half-width-text span6"><br>
                    <h4><strong>RICHIESTA DI DISPONIBILITA' 
                  E/O PRENOTAZIONE</strong></h4>
                    <br><br><br>
                   
                  <p align="justify"><br>
                    <U><STRONG>Availability and/or reservation  request by fax<BR>
                    </STRONG></U>-Print the form  (using the browser integrated print function)<BR>
                  </p>
                    <p align="justify"><U><STRONG>Availability and/or reservation request by phone </STRONG></U>- Mobile:<strong> 333.3968173</strong> - Telephone: <strong>080.9309994</strong> <br><br>
                      In order to confirm the  reservation (by telephone, facsimile message or by e-mail) it is necessary to leave  a 20% deposit of your stay total amount, to be paid to the current account, which  will be communicated later on, by bank transfer.<br>
                  </p>
                </div>
            </div>
        </div>
   <!-- Footer -->

        <!-- Javascript -->

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>