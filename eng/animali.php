<?php
/**
 * Animali Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once '../includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Masseria Torricella',
    'page_description' => '',
    'page_keywords' => '',
    'current_page' => 'animali.htm'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

<div class="slider">
            <div class="container">
                <div class="row">
                    <div class="span10 offset1">
                      <div class="flexslider2">
                            <ul class="slides">
                                <li data-thumb="">
                                    <img src="../assets/img/slider/abitanti1.jpg">
                                    <p class="flex-caption">Small woodland inhabitants </p>
                                </li>
                                <li data-thumb="">
                                    <img src="../assets/img/slider/animali2.jpg">
                                    <p class="flex-caption">Small woodland inhabitants </p>
                                </li>
                                <li data-thumb="">
                                    <img src="../assets/img/slider/animali3.jpg">
                                    <p class="flex-caption">Small woodland inhabitants </p>
                                </li>
                               
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
  <!-- descizione -->
        <div class="about-us container">
            <div class="row">
                <div class="about-us-text span12">
                    <h3>Nature park: woodland creatures</h3>
                    <p><br></p>
                  
              </div>
            </div>
        </div>
    <!-- gallery -->
    <div class="portfolio portfolio-page container">
      <div class="row">
        <div class="portfolio-navigator span12">
          <div class="portfolio-title">
            <h3>Small woodland inhabitants</h3>
          </div>
        </div>
      </div>
      <div class="row">
        <ul class="portfolio-img">
          <li data-id="p-1" data-type="camere" class="span1">
            <div class="work"> <a href="../assets/img/portfolio/ospiti1.jpg" rel="prettyPhoto[pp_gal]" title="Small woodland inhabitants"> <img src="../assets/img/portfolio/ospiti1.jpg" alt=""><i class="icon-search"></i></a>
             
            </div>
          </li>
          <li data-id="p-5" data-type="trullo" class="span1">
            <div class="work"> <a href="../assets/img/portfolio/ospiti2.jpg" rel="prettyPhoto[pp_gal]" title="Small woodland inhabitants"> <img src="../assets/img/portfolio/ospiti2.jpg" alt=""><i class="icon-search"></i></a>
             
            </div>
          </li>
          <li data-id="p-12" data-type="dependance" class="span1">
            <div class="work"> <a href="../assets/img/portfolio/ospiti3.jpg" rel="prettyPhoto[pp_gal]" title="Small woodland inhabitants"> <img src="../assets/img/portfolio/ospiti3.jpg" alt=""><i class="icon-search"></i></a>
             
            </div>
          </li>
          <li data-id="p-11" data-type="casette" class="span1">
            <div class="work"> <a href="../assets/img/portfolio/ospiti4.jpg" rel="prettyPhoto[pp_gal]" title="Small woodland inhabitants"> <img src="../assets/img/portfolio/ospiti4.jpg" alt=""><i class="icon-search"></i></a>
             
            </div>
          </li>
          <li data-id="p-11" data-type="casette" class="span1">
            <div class="work"> <a href="../assets/img/portfolio/ospiti5.jpg" rel="prettyPhoto[pp_gal]" title="Small woodland inhabitants"> <img src="../assets/img/portfolio/ospiti5.jpg" alt=""><i class="icon-search"></i></a>
             
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- menu categoria -->
    <div class="portfolio portfolio-page container">
      <div class="row">
        <div class="portfolio-navigator span12">
          <div class="portfolio-title">
            <h3>Experiencing the woods</h3>
          </div>
        </div>
      </div>
      <div class="row">
        <ul class="portfolio-img">
          <li data-id="p-1" data-type="1" class="span3">
            <div class="work"> <a href="../assets/img/portfolio/percorso1.jpg" rel="prettyPhoto" title="percorso fitness"> <img src="../assets/img/portfolio/percorso1.jpg" alt=""></a>
              <h4>Circuit workout</h4>
              <p><a href="../fitness.php">SEE THE CIRCUIT WORKOUT  &gt;</a></p>
            </div>
          </li>
          <li data-id="p-2" data-type="2" class="span3">
            <div class="work"> <a href="../assets/img/portfolio/percorso2.jpg" rel="prettyPhoto"title="Nature trail"> <img src="../assets/img/portfolio/percorso2.jpg" alt=""></a>
              <h4>Nature trail</h4>
              <p><a href="../natura2.php">SEE THE NATURE TRAIL  &gt;</a></p>
            </div>
          </li>
          <li data-id="p-3" data-type="3" class="span3">
            <div class="work"> <a href="../assets/img/portfolio/percorso3.jpg" rel="prettyPhoto" title="Respect for the wood"> <img src="../assets/img/portfolio/percorso3.jpg" alt=""></a>
              <h4>Respect for the wood</h4>
              <p><a href="../bosco.php">THE RULES  &gt;</a></p>
            </div>
          </li>
          <li data-id="p-4" data-type="4" class="span3">
            <div class="work"> <a href="../assets/img/portfolio/percorso4.jpg" rel="prettyPhoto" title="SMALL WOODLAND INHABITANTS "> <img src="../assets/img/portfolio/percorso4.jpg" alt=""></a>
              <h4>Woodland creatures</h4>
              <p><a href="../animali.php">SMALL WOODLAND INHABITANTS  &gt;</a></p>
            </div>
          </li>
        </ul>
      </div>
    </div>
      <!-- Footer -->
      
      <!-- Javascript -->

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>