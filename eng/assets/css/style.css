/*
 *
 * Template Name: Andia
 * Template URI: http://azmind.com
 * Description: Andia - Responsive Agency Template
 * Author: <PERSON><PERSON>
 * Author URI: http://azmind.com
 *
 */


body {
	background: #fff;
	text-align: center;
	font-family: 'Open Sans', Helvetica, Arial, sans-serif;
	color: #333;
	font-size: 13px;
}

.violet {
	color: #93000B;
}

a {
	color: #990000;
	text-decoration: none;
	-o-transition: all .3s;
	-moz-transition: all .3s;
	-webkit-transition: all .3s;
	-ms-transition: all .3s;
}

a:hover { color: #888; text-decoration: none; }

strong { font-weight: bold; }


/* ----- Header ----- */

.header .navbar {
    margin-bottom: 0;
}

.header .navbar-inner {
    background: #fff;
    border: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.header h1 {
    float: left;
    margin: 0;
    text-align: left;
}

.header a.brand {
	display: inline-block;
    text-indent: -9999px;
    width: 280px;
    height: 148px;
    padding: 10px 0;
    background: url(../img/logo.png) 5px center no-repeat;
}

.header ul.nav {
	font-size: 12px;
	text-transform: uppercase;
}

.header ul.nav li a {
    padding: 30px 20px 10px 20px;
    color: #5d5d5d;
    text-shadow: none;
}

.header ul.nav li.current-page a {
	padding-top: 25px;
	border-top: 5px solid #93000B;
	background: #f8f8f8;
}

.header ul.nav li a:hover {
	color: #fff;
	background-color: #93000B;
	background-image: url();
}

.header ul.nav li a i {
    line-height: 35px;
    color: #aaa;
}

.header ul.nav li a:hover i { color: #fff; }


/* ----- Slider ----- */

.slider {
    margin: 0 auto;
    background: #f8f8f8 url(../img/pattern.jpg) left top repeat;
    -moz-box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
}

.flexslider {
	margin-top: 15px;
	margin-bottom: 15px;
	border: 6px solid #fff;
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	-moz-box-shadow: 0 5px 15px 0 rgba(0,0,0,.05),
        0 -5px 15px 0 rgba(0,0,0,.05);
	-webkit-box-shadow: 0 5px 15px 0 rgba(0,0,0,.05),
        0 -5px 15px 0 rgba(0,0,0,.05);
	box-shadow: 0 5px 15px 0 rgba(0,0,0,.05),
        0 -5px 15px 0 rgba(0,0,0,.05);
}


.flex-caption {
	position: absolute;
	bottom: 20px;
	max-width: 920px;
	padding: 10px 20px;
	margin: 0;
	background: #1d1d1d; /* browsers that don't support rgba */
	background: rgba(0, 0, 0, .7);
	font-size: 14px;
	line-height: 24px;
	color: #eaeaea;
	text-align: left;
	font-style: normal;
}
/* ----- Slider interno ----- */

.slider {
    margin: 0 auto;
    background: #f8f8f8 url(../img/pattern.jpg) left top repeat;
    -moz-box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
}

.flexslider2 {
	margin-top: 10px;
	margin-bottom: 10px;
	border: 6px solid #fff;
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	-moz-box-shadow: 0 5px 15px 0 rgba(0,0,0,.05),
        0 -5px 15px 0 rgba(0,0,0,.05);
	-webkit-box-shadow: 0 5px 15px 0 rgba(0,0,0,.05),
        0 -5px 15px 0 rgba(0,0,0,.05);
	box-shadow: 0 5px 15px 0 rgba(0,0,0,.05),
        0 -5px 15px 0 rgba(0,0,0,.05);
}


.flex-caption {
	position: absolute;
	bottom: 20px;
	max-width: 920px;
	padding: 10px 20px;
	margin: 0;
	background: #1d1d1d; /* browsers that don't support rgba */
	background: rgba(0, 0, 0, .7);
	font-size: 14px;
	line-height: 24px;
	color: #eaeaea;
	text-align: left;
	font-style: normal;
}

/* ----- Presentation ----- */

.presentation {
    margin-top: 30px;
}

.presentation h2 {
    font-family: 'Lobster', cursive;
    font-size: 25px;
    color: #5d5d5d;
}

.presentation p {
    font-size: 18px;
    font-style: italic;
}


/* ----- What we do ----- */

.what-we-do {
    margin-top: 50px;
}

.what-we-do .service {
	padding-bottom: 23px;
	background: #f8f8f8;
	border-bottom-width: 2px;
	border-bottom-style: solid;
	border-bottom-color: #93000B;
}

.what-we-do .service:hover {
    box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05),
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -o-transition: all .5s;
    -moz-transition: all .5s;
    -webkit-transition: all .5s;
    -ms-transition: all .5s;
}

.what-we-do .service .icon-awesome {
	margin-top: 15px;
	font-size: 50px;
	line-height: 50px;
	color: #999999;
}

.what-we-do .service h4 {
    margin-top: 5px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #5d5d5d;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.what-we-do .service p {
    padding-bottom: 10px;
    line-height: 24px;
}

.what-we-do .service a {
	padding: 5px 22px;
	color: #fff;
	font-style: italic;
	text-decoration: none;
	-moz-box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	-webkit-box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	background-color: #93000B;
}

.what-we-do .service a:hover {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.what-we-do .service a:active {
    -moz-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
}


/* ----- Portfolio ----- */

.portfolio {
	margin-top: 50px;
	margin-bottom: 50px;
}

.portfolio-title {
    background: url(../img/line.png) left center repeat-x;
}

.portfolio-title h3 {
	width: 220px;
	background: #FFFCEC;
	font-family: 'Lobster', cursive;
	font-size: 24px;
	color: #5d5d5d;
	padding-left:0px;
	margin-top: 0;
	margin-right: auto;
	margin-bottom: 0;
	margin-left: auto;
	
}

.portfolio .work {
	margin-top: 40px;
	padding-bottom: 10px;
	background: #f8f8f8;
	border-bottom-width: 2px;
	border-bottom-style: solid;
	border-bottom-color: #93000B;
}

.portfolio .work:hover img {
    opacity: 0.7;
    -o-transition: all .3s;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -ms-transition: all .3s;
}

.portfolio .work:hover {
    box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05),
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -o-transition: all .5s;
    -moz-transition: all .5s;
    -webkit-transition: all .5s;
    -ms-transition: all .5s;
}

.portfolio .work .icon-awesome {
    margin-top: 15px;
    font-size: 22px;
    line-height: 22px;
}

.portfolio .work .icon-awesome a {
    display: inline-block;
    padding: 5px 9px;
    background: #93000B; 
    color: #fff;
    -moz-border-radius: 19px;
    -webkit-border-radius: 19px;
    border-radius: 19px;
    -moz-box-shadow:
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
}

.portfolio .work .icon-awesome a:hover {
    background: #5d5d5d;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.portfolio .work .icon-awesome a:active {
    -moz-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
}

.portfolio .work h4 {
	margin-top: 10px;
	font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
	font-size: 14px;
	color: #5d5d5d;
	text-transform: uppercase;
	text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.portfolio .work p {
	line-height: 14px;
	font-style: normal;
	font-weight: bold;
}


/* ----- Testimonials ----- */

.testimonials {
    margin-top: 50px;
    padding-bottom: 50px;
}

.testimonials-title {
    background: url(../img/line.png) left center repeat-x;
}

.testimonials-title h3 {
    width: 180px;
    margin: 0 auto;
    background: #FFFCEC;
	padding-left:0px;
    font-family: 'Lobster', cursive;
    font-size: 24px;
    color: #5d5d5d;
}

.testimonial-list {
	margin-top: 30px;
	text-align: center;
}

.testimonial-list img {
    float: left;
    margin: 10px 0 0 10px;
    border: 3px solid #eaeaea;
}

.testimonial-list p {
	padding: 0 60px 0 80px;
	font-size: 14px;
	line-height: 30px;
	font-style: normal;
}

.testimonial-list .nav-tabs {
    width: 200px;
    float: right;
    border: 0;
}

.testimonial-list .nav-tabs li {
    margin-right: 6px;
}

.testimonial-list .nav-tabs li a {
    width: 12px;
    height: 12px;
    padding: 0;
    background: #eaeaea;
    border: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.testimonial-list .nav-tabs li a:hover { border: 0; background: #ddd; }
.testimonial-list .nav-tabs li.active a {
	background-color: #00468C;
}

/* ----- Testimonials 2----- */

.testimonials2 {
    margin-top: 50px;
    padding-bottom: 50px;
}

.testimonials2-title {
    background: url(../img/line.png) left center repeat-x;
}

.testimonials2-title h3 {
    width: 180px;
    margin: 0 auto;
    background: #fff;
    font-family: 'Lobster', cursive;
    font-size: 24px;
    color: #5d5d5d;
}

.testimonial2-list {
	margin-top: 30px;
	text-align: left;
}

.testimonial2-list img {
    float: left;
    margin: 10px 0 0 10px;
    border: 3px solid #eaeaea;
}

.testimonial2-list p {
	font-size: 14px;
	line-height: 30px;
	font-style: normal;
	padding-top: 0;
	padding-right: 50px;
	padding-bottom: 0;
	padding-left: 350px;
}

.testimonial2-list .nav-tabs {
    width: 200px;
    float: right;
    border: 0;
}

.testimonial2-list .nav-tabs li {
    margin-right: 6px;
}

.testimonial2-list .nav-tabs li a {
    width: 12px;
    height: 12px;
    padding: 0;
    background: #eaeaea;
    border: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

.testimonial2-list .nav-tabs li a:hover { border: 0; background: #ddd; }
.testimonial2-list .nav-tabs li.active a {
	background-color: #00468C;
}

/* ----- Footer ----- */

footer {
    margin: 0 auto;
    padding-bottom: 10px;
    background: #f8f8f8 url(../img/pattern.jpg) left top repeat;
    -moz-box-shadow: 0 5px 15px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow: 0 5px 15px 0 rgba(0,0,0,.05) inset;
    box-shadow: 0 5px 15px 0 rgba(0,0,0,.05) inset;
}

footer .widget {
    margin-top: 20px;
    text-align: left;
}

footer .widget h4 {
    margin-top: 20px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #5d5d5d;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

footer .widget p {
    line-height: 24px;
}

footer .widget i {
    padding-right: 7px;
}

/* Twitter feed */
.show-tweets {
    margin: 0;
    overflow-y: hidden;
}

.tweet_list {
    height: 10em;
    margin: 0;
    padding: 0;
    overflow-y: hidden;
    list-style: none;
}
	
.tweet_list li {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    list-style-type: none;
    line-height: 24px;
}
		
.tweet_list .tweet_avatar {
    float: left;
}
		
.tweet_list .tweet_avatar img {
    vertical-align: middle;
}

/* Flickr feed */
.flickr-feed {
    margin: 16px 0 0 0;
    overflow: hidden
}

.flickr-feed li {
    float: left;
    padding: 0 4px 4px 0;
    list-style: none;
}

.flickr-feed li img {
    width: 50px;
    border: 2px solid #eaeaea;
}

.flickr-feed a:hover {
    opacity: 0.7;
}


footer .footer-border {
    margin-top: 30px;
    border-top: 1px dashed #ddd;
}

footer .copyright {
    margin-top: 15px;
    text-align: left;
}

footer .social {
	margin-top: 10px;
	text-align: left;
}

footer .social a { display: inline-block; width: 24px; height: 24px; margin: 0 0 0 8px; vertical-align: middle; }

footer .social a.gal1 { background: url(../img/social-icons/gal1.jpg) left bottom no-repeat; }
footer .social a.gal2 { background: url(../img/social-icons/gal2.jpg) left bottom no-repeat; }
footer .social a.gal3 { background: url(../img/social-icons/gal3.jpg) left bottom no-repeat; }

footer .social a.deu { background: url(../img/social-icons/deu.png) left bottom no-repeat; }
footer .social a.eng { background: url(../img/social-icons/eng.png) left bottom no-repeat; }
footer .social a.fra { background: url(../img/social-icons/fra.png) left bottom no-repeat; }
footer .social a.spa { background: url(../img/social-icons/spa.png) left bottom no-repeat; }
footer .social a.flickr { background: url(../img/social-icons/flickr.png) left bottom no-repeat; }
footer .social a.forrst { background: url(../img/social-icons/forrst.png) left bottom no-repeat; }
footer .social a.vimeo { background: url(../img/social-icons/vimeo.png) left bottom no-repeat; }
footer .social a.linkedin { background: url(../img/social-icons/linkedin.png) left bottom no-repeat; }
footer .social a.ita { background: url(../img/social-icons/ita.png) left bottom no-repeat; }
footer .social a.email { background: url(../img/social-icons/email.png) left bottom no-repeat; }
footer .social a.github { background: url(../img/social-icons/github.png) left bottom no-repeat; }
footer .social a.behance { background: url(../img/social-icons/behance.png) left bottom no-repeat; }
footer .social a.googleplus { background: url(../img/social-icons/googleplus.png) left bottom no-repeat; }
footer .social a.youtube { background: url(../img/social-icons/youtube.png) left bottom no-repeat; }
footer .social a.skype { background: url(../img/social-icons/skype.png) left bottom no-repeat; }
footer .social a.tumblr { background: url(../img/social-icons/tumblr.png) left bottom no-repeat; }

footer .social a:hover { background-position: left top; }


/* ----- Page title ----- */

.page-title {
    margin: 0 auto;
    padding: 30px 0 35px 0;
    background: #f8f8f8 url(../img/pattern.jpg) left top repeat;
    -moz-box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05) inset,
        0 -5px 15px 0 rgba(0,0,0,.05) inset;
    text-align: left;
}

.page-title h2 {
    display: inline;
    margin-left: 10px;
    font-family: 'Lobster', cursive;
    font-size: 24px;
    color: #5d5d5d;
    text-shadow: 0 1px 0 rgba(255, 255, 255, .7);
    vertical-align: middle;
}

.page-title p {
    display: inline;
    margin-left: 5px;
    font-size: 14px;
    font-style: italic;
    vertical-align: middle;
}

.page-title-icon {
    margin-left: 20px;
    font-size: 46px;
    color: #ccc;
    vertical-align: middle;
}


/********** ----- ABOUT PAGE ----- **********/

/* ----- About us text ----- */

.about-us {
	margin-top: 20px;
	margin-bottom: 30px;
}

.about-us-text {
	text-align: left;
	padding-top: 10px;
	padding-right: 0;
	padding-bottom: 5px;
	padding-left: 0;
}

.about-us-text h4 {
    margin-top: 25px;
    padding: 0 20px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 16px;
    color: #5d5d5d;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.about-us-text p {
    padding: 0 20px;
    line-height: 28px;
    font-size: 13px;
}

/* ----- Meet our team ----- */

.team {
    margin-top: 30px;
}

.team-title {
    background: url(../img/line.png) left center repeat-x;
}

.team-title h3 {
    width: 220px;
    margin: 0 auto;
    background: #fff;
    font-family: 'Lobster', cursive;
    font-size: 24px;
    color: #5d5d5d;
}

.team-text {
    margin-top: 40px;
    padding-bottom: 20px;
    background: #f8f8f8;
    border-bottom: 2px solid #9d426b;
}

.team-text:hover img {
    opacity: 0.7;
    -o-transition: all .3s;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -ms-transition: all .3s;
}

.team-text:hover {
    box-shadow:
        0 5px 15px 0 rgba(0,0,0,.05),
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -o-transition: all .5s;
    -moz-transition: all .5s;
    -webkit-transition: all .5s;
    -ms-transition: all .5s;
}

.team-text .social-links {
    margin-top: 15px;
}

.team-text .social-links a { display: inline-block; width: 24px; height: 24px; margin: 0 4px; vertical-align: middle; }

.team-text .social-links a.twitter { background: url(../img/social-icons/twitter.png) left top no-repeat; }
.team-text .social-links a.dribbble { background: url(../img/social-icons/dribbble.png) left top no-repeat; }
.team-text .social-links a.pinterest { background: url(../img/social-icons/pinterest.png) left top no-repeat; }
.team-text .social-links a.flickr { background: url(../img/social-icons/flickr.png) left top no-repeat; }
.team-text .social-links a.forrst { background: url(../img/social-icons/forrst.png) left top no-repeat; }
.team-text .social-links a.vimeo { background: url(../img/social-icons/vimeo.png) left top no-repeat; }
.team-text .social-links a.linkedin { background: url(../img/social-icons/linkedin.png) left top no-repeat; }
.team-text .social-links a.facebook { background: url(../img/social-icons/facebook.png) left top no-repeat; }
.team-text .social-links a.email { background: url(../img/social-icons/email.png) left top no-repeat; }
.team-text .social-links a.behance { background: url(../img/social-icons/behance.png) left top no-repeat; }
.team-text .social-links a.googleplus { background: url(../img/social-icons/googleplus.png) left top no-repeat; }
.team-text .social-links a.youtube { background: url(../img/social-icons/youtube.png) left top no-repeat; }
.team-text .social-links a.skype { background: url(../img/social-icons/skype.png) left top no-repeat; }
.team-text .social-links a.tumblr { background: url(../img/social-icons/tumblr.png) left top no-repeat; }

.team-text .social-links a:hover { background-position: left bottom; }

.team-text h4 {
    margin-top: 20px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #5d5d5d;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.team-text p {
    line-height: 24px;
    font-style: italic;
}


/********** ----- CONTACT PAGE ----- **********/

/* ----- Form ----- */

.contact-us {
    margin-top: 20px;
    padding-bottom: 50px;
    text-align: left;
}

.contact-us h4 {
    margin-top: 25px;
    padding: 0 20px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 16px;
    color: #5d5d5d;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.contact-us p {
    padding: 0 20px;
    line-height: 28px;
    font-size: 13px;
}

.contact-form {
    padding: 10px 0;
}

.contact-form p {
    margin-top: 15px;
}

.contact-us form {
    margin-top: 25px;
    padding: 0 20px;
}

.contact-us form input, .contact-us form textarea {
    width: 90%;
    height: 24px;
    border: 1px solid #ddd;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-family: 'Open Sans', Helvetica, Arial, sans-serif;
    color: #888;
    font-size: 13px;
    font-style: italic;
}

.contact-us form input:focus, .contact-us form textarea:focus {
    border: 1px solid #bbb;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.contact-us form textarea {
    height: 160px;
}

.contact-us form button {
    width: 120px;
    height: 30px;
    margin-top: 15px;
    background: #9d426b;
    border: 0;
    font-family: 'Open Sans', Helvetica, Arial, sans-serif;
    font-size: 13px;
    color: #fff;
    -moz-box-shadow:
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -o-transition: all .3s;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -ms-transition: all .3s;
}

.contact-us form button:hover {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.contact-us form button:active {
    -moz-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
}

.contact-us form label {
    margin-top: 10px;
    font-size: 13px;
}

.contact-us form input:-moz-placeholder, .contact-us form textarea:-moz-placeholder { color: #ccc; }
.contact-us form input:-ms-input-placeholder, .contact-us form textarea:-ms-input-placeholder { color: #ccc; }
.contact-us form input::-webkit-input-placeholder, .contact-us form textarea::-webkit-input-placeholder { color: #ccc; }

/* ----- Google maps ----- */

.map {
    margin: 20px 20px 40px 20px;
    height: 500px;
    border: 5px solid #f8f8f8;
}


/********** ----- SERVICES PAGE ----- **********/

/* ----- Services full width text ----- */

.services-full-width {
    margin-top: 20px;
}

.services-full-width-text {
    padding: 10px 0 0 0;
    text-align: left;
}

.services-full-width-text h4 {
    margin-top: 25px;
    padding: 0 20px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 16px;
    color: #5d5d5d;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.services-full-width-text p {
    padding: 0 20px;
    line-height: 28px;
    font-size: 13px;
}

/* ----- Services half width text ----- */

.services-half-width {
    margin-top: 20px;
}

.services-half-width-text {
    padding: 10px 0;
    text-align: left;
}

.services-half-width-text h4 {
	margin-top: 25px;
	padding: 0 20px;
	font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
	font-size: 16px;
	color: #990000;
	text-transform: uppercase;
	text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

.services-half-width-text p {
    padding: 0 20px;
    line-height: 28px;
    font-size: 13px;
}

/* ----- Call to action ----- */

.call-to-action {
    margin-top: 20px;
    padding-bottom: 50px;
}

.call-to-action-text {
	text-align: left;
	overflow: hidden;
	background-color: #E6E1AA;
	padding-top: 15px;
	padding-right: 0;
	padding-bottom: 5px;
	padding-left: 0;
}

.call-to-action-text:hover {
	box-shadow: 0 3px 10px 0 rgba(0,0,0,.05), 0 1px 25px 0 rgba(0,0,0,.05) inset, 0 -1px 25px 0 rgba(0,0,0,.08) inset;
	-o-transition: all .5s;
	-moz-transition: all .5s;
	-webkit-transition: all .5s;
	-ms-transition: all .5s;
}

.call-to-action-text .ca-text, .call-to-action-text .ca-button {
	float: left;
	padding: 0 0 0 35px;
	line-height: 30px;
	font-size: 15px;
	font-style: normal;
}

.call-to-action-text .ca-button {
    float: right;
    padding: 0 35px 0 0;
}

.call-to-action-text .ca-button a {
	padding: 5px 22px;
	color: #fff;
	text-decoration: none;
	-moz-box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	-webkit-box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	background-color: #990000;
}

.call-to-action-text .ca-button a:hover {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.call-to-action-text .ca-button a:active {
    -moz-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
}




.call-to-action-text .ca-text, .call-to-action-text .ca-button2 {
	float: left;
	padding: 0 0 0 35px;
	line-height: 30px;
	font-size: 15px;
	font-style: normal;
}

.call-to-action-text .ca-button2 {
    float: right;
    padding: 0 35px 0 0;
}

.call-to-action-text .ca-button2 a {
	padding: 5px 22px;
	color: #fff;
	text-decoration: none;
	-moz-box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	-webkit-box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	box-shadow: 0 1px 25px 0 rgba(0,0,0,.05) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
	background-color: #000000;
}

.call-to-action-text .ca-button2 a:hover {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.call-to-action-text .ca-button2 a:active {
    -moz-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    -webkit-box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
    box-shadow:
        0 5px 10px 0 rgba(0,0,0,.15) inset,
        0 -1px 25px 0 rgba(0,0,0,.05) inset;
}


/********** ----- PORTFOLIO PAGE ----- **********/

.portfolio-page {
    margin-top: 20px;
    padding-bottom: 50px;
}

.portfolio-page h4.filter-portfolio {
    margin-top: 35px;
    padding: 0 20px;
    font-family: 'Droid Sans', Helvetica, Arial, sans-serif;
    font-size: 16px;
    color: #5d5d5d;
    text-align: left;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(255,255,255,.7);
}

ul.portfolio-img {
    margin: 0;
    overflow: hidden;
}

ul.portfolio-img li {
    list-style: none;
}

.filter-portfolio a { color: #5d5d5d; text-decoration: none; }
.filter-portfolio a:hover { color: #9d426b; text-decoration: none; }

.filter-portfolio a#active-imgs { color: #9d426b; }
.filter-portfolio a#active-imgs:hover { color: #5d5d5d; }




/* ----- Media queries ----- */

@media (min-width: 980px) and (max-width: 1200px) {

    .flex-caption {
        max-width: 700px;
    }

    .what-we-do .service p, .portfolio .work p {
        padding-left: 10px;
        padding-right: 10px;
    }

    .call-to-action-text {
        padding-bottom: 30px;
    }

    .call-to-action-text .ca-text {
        padding: 0 35px;
    }

    .call-to-action-text .ca-button {
        margin-top: 10px;
    }

}

@media (min-width: 768px) and (max-width: 979px) {

    .header ul.nav li a {
        padding: 15px 20px 15px 20px;
        font-weight: normal;
        text-align: left;
        -moz-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    .header ul.nav li.current-page a {
        padding-top: 15px;
    }

    .header ul.nav li a i, .header ul.nav li a br {
        display: none;
    }

    .flex-caption {
        max-width: 500px;
    }

    .what-we-do .service p, .portfolio .work p {
        padding-left: 10px;
        padding-right: 10px;
    }

    .call-to-action-text {
        padding-bottom: 30px;
    }

    .call-to-action-text .ca-text {
        padding: 0 35px;
    }

    .call-to-action-text .ca-button {
        margin-top: 10px;
    }

}

@media (max-width: 767px) {

    body {
        padding-left: 0;
        padding-right: 0;
    }

    .slider, .what-we-do, .portfolio {
        padding: 0 20px;
    }

    .what-we-do {
        margin-top: 30px;
    }

    .header ul.nav li a {
        padding: 15px 20px 15px 20px;
        font-weight: normal;
        text-align: left;
        -moz-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    .header ul.nav li.current-page a {
        padding-top: 15px;
    }

    .header ul.nav li a i, .header ul.nav li a br {
        display: none;
    }

    .flex-caption {
        display: none;
    }

    .presentation, footer, .testimonials {
        padding-left: 20px;
        padding-right: 20px;
    }

    .presentation p {
        line-height: 30px;
    }

    .what-we-do .service p, .portfolio .work p {
        padding-left: 10px;
        padding-right: 10px;
    }

    .what-we-do .service .icon-awesome {
        padding-top: 10px;
    }

    .page-title p {
        display: block;
        margin-top: 10px;
        margin-left: 20px;
    }

    .call-to-action-text {
        padding-bottom: 30px;
    }

    .call-to-action-text .ca-text {
        padding: 0 35px;
    }

    .call-to-action-text .ca-button {
        margin-top: 10px;
    }

    .portfolio-page {
        padding-bottom: 50px;
    }

    .tweet_list {
        height: 6em;
    }

}

@media (max-width: 480px) {

    .tweet_list {
        height: 10em;
    }

}
.media-container {
	position: relative;
	padding-bottom: 56.25%;
	height: 0;
	overflow: hidden;
        width:100%;
}

.media-container iframe,  
.media-container object,  
.media-container embed {
	position: absolute;
	padding-left:18px;
	top: 0;
	left: 0;
	width: 98%;
	height: 98%;
}
