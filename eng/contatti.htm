<!DOCTYPE html>
<html lang="en">

    <head>

        <meta charset="utf-8">
        <title>Masseria Torricella</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <meta name="author" content="">

        <!-- CSS -->
        <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Open+Sans:400italic,400">
        <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Droid+Sans">
        <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Lobster">
        <link rel="stylesheet" href="assets/bootstrap/css/bootstrap.min.css">
        <link rel="stylesheet" href="assets/prettyPhoto/css/prettyPhoto.css">
        <link rel="stylesheet" href="assets/css/flexslider.css">
        <link rel="stylesheet" href="assets/css/font-awesome.css">
        <link rel="stylesheet" href="assets/css/style.css">

        <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
        <!--[if lt IE 9]>
            <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
        <![endif]-->

        <!-- Favicon and touch icons -->
        <link rel="shortcut icon" href="assets/ico/favicon.ico">
        <link rel="apple-touch-icon-precomposed" sizes="144x144" href="assets/ico/apple-touch-icon-144-precomposed.png">
        <link rel="apple-touch-icon-precomposed" sizes="114x114" href="assets/ico/apple-touch-icon-114-precomposed.png">
        <link rel="apple-touch-icon-precomposed" sizes="72x72" href="assets/ico/apple-touch-icon-72-precomposed.png">
        <link rel="apple-touch-icon-precomposed" href="assets/ico/apple-touch-icon-57-precomposed.png">
        <style type="text/css">
        body,td,th {
	font-family: "Open Sans", Helvetica, Arial, sans-serif;
}
body {
	background-color: #FFFCEC;
	background-repeat: no-repeat;
}
        </style>
        <script type="text/javascript">
<!--
function openPictureWindow_Fever(imageType,imageName,imageWidth,imageHeight,alt,posLeft,posTop) {  // v4.01
	newWindow = window.open("","newWindow","width="+imageWidth+",height="+imageHeight+",scrollbars=no,left="+posLeft+",top="+posTop);
	newWindow.document.open();
	newWindow.document.write('<html><title>'+alt+'</title><body bgcolor="#FFFFFF" leftmargin="0" topmargin="0" marginheight="0" marginwidth="0" onBlur="self.close()">'); 
	if (imageType == "swf"){
	newWindow.document.write('<object classid=\"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000\" codebase=\"http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=5,0,0,0\" width=\"'+imageWidth+'\" height=\"'+imageHeight+'\">');
	newWindow.document.write('<param name=movie value=\"'+imageName+'\"><param name=quality value=high>');
	newWindow.document.write('<embed src=\"'+imageName+'\" quality=high pluginspage=\"http://www.macromedia.com/shockwave/download/index.cgi?P1_Prod_Version=ShockwaveFlash\" type=\"application/x-shockwave-flash\" width=\"'+imageWidth+'\" height=\"'+imageHeight+'\">');
	newWindow.document.write('</embed></object>');	}else{
	newWindow.document.write('<img src=\"'+imageName+'\" width='+imageWidth+' height='+imageHeight+' alt=\"'+alt+'\">'); 	}
	newWindow.document.write('<!-- inizio banner policy cookie  -->
<link href="http://www.acti-web.com/privacy/stili.css" rel="stylesheet" type="text/css">
<iframe id='cookie_iframe' src="http://www.acti-web.com/privacy/privacy_global.php?s=masseriatorricella"></iframe>
<!-- fine banner policy cookie -->
    </body></html>');
	newWindow.document.close();
	newWindow.focus();
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';
      } else if (test!='R') { num = parseFloat(val);
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' obbligatorio.\n'; }
  } 
  
 errors+=test_privacy();
  
  if (errors) alert('Errori nella compilazione del modulo:\n'+errors);
  document.MM_returnValue = (errors == '');
}

//funzione checkbox privacy
// - il campo del check si deve chiamare check_privacy
// - prima dell'ultimo if in MM_validateForm bisogna aggiungere questa riga: errors+=test_privacy();

function test_privacy(){
if (!document.all.check_privacy.checked){ 
	vvv='- Per continuare devi autorizzare il trattamento dei dati personali.'
} 
else { 
	vvv=''; 
}
return vvv; 
}

//-->
</script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-XQ3WQSR0K6"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-XQ3WQSR0K6');
</script>
</head>

    <body>

        <!-- Header -->
        <div class="container">
            <div class="header row">
                <div class="span12">
                    <div class="navbar">
                        <div class="navbar-inner">
                            <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                            </a>
                             <h1>
                                <a class="brand"></a>
                            </h1>
                             <div class="nav-collapse collapse">
                               <ul class="nav pull-right">
                                 <li class="current-page"> <a href="index.html">Home</a></li>
                                 <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >THE FARMHOUSE<b class="caret"></b></a>
                                   <ul class="dropdown-menu">
                                     <li><a href="presentazione.htm">OVERVIEW</a></li>
                                     <li class="divider"></li>
                                     <li><a href="masseria.htm">THE FARMHOUSE</a></li>
                                     <li class="divider"></li>
                                     <li><a href="storia.htm">HISTORY AND ORIGINS</a></li>
                                     <li class="divider"></li>
                                     <li><a href="posizione.htm">LOCATION</a></li>
                                     <li class="divider"></li>
                                     <li><a href="dintorni.htm">THE SURROUNDING AREA</a></li>
                                   </ul>
                                 </li>
                                 <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >HOSPITALITY<b class="caret"></b></a>
                                   <ul class="dropdown-menu">
                                     <li><a href="camere.htm">Rooms</a></li>
                                     <li class="divider"></li>
                                     <li><a href="tariffe.htm">Room rates</a></li>
                                     <li class="divider"></li>
                                     <li><a href="casevacanza.htm">Holiday homes</a></li>
                                     <li class="divider"></li>
                                     <li><a href="tariffe2.htm">Holiday homes rates</a></li>
                                     <li class="divider"></li>
                                     <li><a href="prenotazioni.htm">Booking</a></li>
                                   </ul>
                                 </li>
                                 <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >AMENITIES<b class="caret"></b></a>
                                   <ul class="dropdown-menu">
                                     <li><a href="ristorante.htm">THE RESTAURANT</a></li>
                                     <li class="divider"></li>
                                     <li><a href="piscina.htm">THE POOL</a></li>
                                     <li class="divider"></li>
                                     <li><a href="servizi.htm">SERVICES</a></li>
                                     <li class="divider"></li>
                                     <li><a href="natura.htm">NATURE PARK</a></li>
                                   </ul>
                                 </li>
                                 <li> <a href="https://www.masseriatorricellashop.it/it/">shop on line</a> </li>
                                 <li> <a href="gallery.htm">PHOTOGALLERY</a></li>
                                 <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >CONTACTS<b class="caret"></b></a>
                                   <ul class="dropdown-menu">
                                     <li><a href="contatti.htm">INFO</a></li>
                                     <li class="divider"></li>
                                     <li><a href="dovesiamo.htm">OUR LOCATION</a></li>
                                   </ul>
                                 </li>
                               </ul>
                             </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Slider -->
    <div class="page-title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h3>Contacts: request information<em></em></h3>
          </div>
        </div>
      </div>
    </div>
    <!-- Contact Us -->
        <div class="contact-us container">
            <div class="row">
                <div class="services-half-width-text span6">
                   
                  
                </div>
                <div class="services-half-width-text span6">
                    <h4>contact details</h4>
                    <br><br><br>
                   
                    <p><strong>Azienda Agrituristica Masseria Torricella</strong><br>

S.P. Canale di Pirro, 19 70011 Alberobello (BA)<br>
<strong>Tel. +39 080.9309994</strong><br>
<strong>Mobile +39 333.3968173</strong><br>
P.IVA 04140280720<br>

E-mail: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </div>
  
    <!-- Footer -->
      <footer>
        <div class="container">
          <div class="row">
            <div class="widget span3">
              <h4>Contacts</h4>
              <p><i class="icon-map-marker"></i>S.P. Canale di Pirro, 19 70011 Alberobello (BA) - APULIA - ITALY </p>
              <p><i class="icon-phone"></i> Cell. 3333968173</p>
              <p><i class="icon-envelope-alt"></i> Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
              <p>
              <h4>&nbsp;</h4>
              <h4> Realization:</h4>
              <a href="http://www.acti-web.com">Actiweb</a>
              </p>
            </div>
            <div class="widget span3">
              <h4> Language: </h4>
              <div class="social span8"> <a class="ita" href="../index.html"></a> <a class="eng" href="index.html"></a> <a class="deu" href="../deu/index.html"></a> <a class="spa" href="../spa/index.html"></a> <a class="fra" href="../fra/index.html"></a></div><h4>&nbsp;</h4>
                  
                  <h4>PHOTOGALLERY</h4>
                    <div class="social span8"> <a class="gal1" href="gallery.htm"></a> <a class="gal2" href="gallery2.htm"></a> <a class="gal3" href="gallery3.htm"></a></div><h4>&nbsp;</h4>
                </div>
                <div class="widget span3"><iframe src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fmasseriatorricellaalberobello%2F&tabs=timeline&width=340&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId" width="340" height="500" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowTransparency="true"></iframe>
                </div>
                <div class="widget span3">
                  <iframe src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fmasseriatorricellaviverebio%2F&tabs=timeline&width=340&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId" width="340" height="500" style="border:none;overflow:hidden;margin-left:45px" scrolling="no" frameborder="0" allowTransparency="true"></iframe>
                </div>
              </div>
              <div class="footer-border"></div>
                <div class="row">
                    
                    
                    </div>
                </div>
            </div>
    </footer>
      <!-- Javascript -->
    <script src="assets/js/jquery-1.8.2.min.js"></script>
    <script src="assets/bootstrap/js/bootstrap.min.js"></script>
    <script src="assets/js/jquery.flexslider.js"></script>
    <script src="assets/prettyPhoto/js/jquery.prettyPhoto.js"></script>
    <script src="assets/js/scripts.js"></script>

    <!-- inizio banner policy cookie  -->
<link href="http://www.acti-web.com/privacy/stili.css" rel="stylesheet" type="text/css">
<iframe id='cookie_iframe' src="http://www.acti-web.com/privacy/privacy_global.php?s=masseriatorricella"></iframe>
<!-- fine banner policy cookie -->
    </body>

</html>

