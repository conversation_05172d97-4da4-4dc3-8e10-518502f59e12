<?php
/**
 * Dependance Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once '../includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Masseria Torricella',
    'page_description' => '',
    'page_keywords' => '',
    'current_page' => 'dependance.htm'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

<div class="slider">
            <div class="container">
                <div class="row">
                    <div class="span10 offset1">
                      <div class="flexslider2">
                            <ul class="slides">
                                <li data-thumb="">
                                    <img src="../assets/img/slider/camere3.jpg">
                                    <p class="flex-caption">THE GUESTHOUSES</p>
                                </li>
                                <li data-thumb="">
                                    <img src="../assets/img/slider/dependance.jpg">
                                    <p class="flex-caption">THE GUESTHOUSES</p>
                                </li>
                                
                        </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- descrizione -->
        <div class="about-us container">
            <div class="row">
             <div class="services-half-width-text span12">
              <h3>Hospitality: THE GUESTHOUSES </h3><br>
              <p align="justify">For those who prefers more privacy, there are some GUESTHOUSES at few meters from the farm central building which allow to have a “deeper” contact with nature and they all have a veranda overlooking the country.  All GUESTHOUSES have:<br>
                <strong>- private facilities,</strong><br>
                <strong>- TV,</strong><br>
                <strong>- hairdryer,</strong><br>
                <strong>- air conditioning and heating</strong><br>
                <strong>- cleaning service and daily
  towels and linen change</strong> .<br>
<br>
              </p>
            </div>
          </div>
        </div>
         
          <!-- gallery -->
        <div class="portfolio portfolio-page container">
          <div class="row">
            <div class="portfolio-navigator span12">
              <div class="portfolio-title">
                <h3>Le immagini delle dependance</h3>
              </div>
            </div>
          </div>
            <div class="row">
                <ul class="portfolio-img">
                  <li data-id="p-1" data-type="camere" class="span3">
                    <div class="work"> <a href="assets/img/portfolio/dependance1.jpg" rel="prettyPhoto[pp_gal]" title="THE GUESTHOUSES: Verandah"> <img src="../assets/img/portfolio/dependance1-p.jpg" alt=""> </a>
                      <h4>Verandah</h4>
                    </div>
                  </li>
                    <li data-id="p-5" data-type="trullo" class="span3">
                      <div class="work"> <a href="assets/img/portfolio/dependance3.jpg" rel="prettyPhoto[pp_gal]" title="THE GUESTHOUSES: Verandah"> <img src="../assets/img/portfolio/dependance3-p.jpg" alt=""> </a>
                        <h4>Verandah</h4>
                      </div>
                    </li>
             <li data-id="p-12" data-type="dependance" class="span3">
               <div class="work"> <a href="assets/img/portfolio/dependance2.jpg" rel="prettyPhoto[pp_gal]" title="THE GUESTHOUSES: Inside"> <img src="../assets/img/portfolio/dependance2-p.jpg" alt=""> </a>
                 <h4>Inside</h4>
               </div>
             </li>
                  
                </ul>
            </div>
        </div>
          <!-- Call To Action -->
        <div class="call-to-action container">
            <div class="row">
              <div class="call-to-action-text span12">
                <div class="ca-text">
                  <p>Daily room rates</p>
                </div>
                <div class="ca-button"><a href="prenotazioni.php">Booking </a></div>
                
              </div>
            </div>
			
			<div id="slope-bl" data-id="8c73d3c9-df98-41f2-8b24-618e83ff232a" data-lang="en">             </div>
			
        </div>
        <div class="portfolio portfolio-page container">
          <div class="row">
            <div class="portfolio-navigator span12">
              <div class="portfolio-title">
                <h3> Types of rooms</h3>
              </div>
            </div>
          </div>
          <div class="row">
            <ul class="portfolio-img">
              <li data-id="p-1" data-type="camere" class="span3">
                <div class="work"> <img src="../assets/img/portfolio/camera1-p.jpg" alt="">
                  <h4>Rooms in the farmhouse </h4>
                  <p><a href="camere1.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
              <li data-id="p-5" data-type="trullo" class="span3">
                <div class="work"> <img src="../assets/img/portfolio/trullo1-p.jpg" alt="">
                  <h4>the "trullo"</h4>
                  <p><a href="trullo.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
              <li data-id="p-12" data-type="dependance" class="span3">
                <div class="work"> <img src="../assets/img/portfolio/dependance1-p.jpg" alt="">
                  <h4>The guest houses</h4>
                  <p><a href="dependance.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
              <li data-id="p-11" data-type="casette" class="span3">
                <div class="work"> <img src="../assets/img/portfolio/casette4-p.jpg" alt="">
                  <h4>The Quercia cottages </h4>
                  <p><a href="casette.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
            </ul>
          </div>
        </div>
         <!-- Footer -->

        <!-- Javascript -->

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>