<?php
/**
 * Camere1 Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once 'includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Masseria Torricella',
    'page_description' => '',
    'page_keywords' => '',
    'current_page' => 'camere1.htm'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

<div class="slider">
            <div class="container">
                <div class="row">
                    <div class="span10 offset1">
                      <div class="flexslider2">
                            <ul class="slides">
                                <li data-thumb="">
                                    <img src="assets/img/slider/camere1_1.jpg">
                                    <p class="flex-caption">the rooms in the farmhouse</p>
                                </li>
                                <li data-thumb="">
                                    <img src="assets/img/slider/camere1_2.jpg">
                                    <p class="flex-caption">the rooms in the farmhouse</p>
                                </li>
                                <li data-thumb="">
                                    <img src="assets/img/slider/camere1_3.jpg">
                                    <p class="flex-caption">the rooms in the farmhouse</p>
                                </li>
                               
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- descrizione -->
        <div class="about-us container">
            <div class="row">
             <div class="services-half-width-text span6">
              <h3>Hospitality: the rooms in the farmhouse</h3><br>
              <p align="justify">Comfortable bedrooms, furnished with good taste and in real country style, with wrought iron beds and hand embroidered linen.  All rooms have:<br>

                <strong>- private facilities,</strong><br>
                <strong>- TV,</strong><br>
                <strong>- hairdryer,</strong><br>
                <strong>- air conditioning and heating</strong><br>
 <strong>- cleaning service and daily
  towels and linen change</strong>
.<br>
<br>
              </p>
            </div>
          </div>
        </div>
         
          <!-- gallery -->
        <div class="portfolio portfolio-page container">
          <div class="row">
            <div class="portfolio-navigator span12">
              <div class="portfolio-title">
                <h3>Rooms in the farmhouse</h3>
              </div>
            </div>
          </div>
            <div class="row">
                <ul class="portfolio-img">
                  <li data-id="p-1" data-type="camere" class="span1">
                    <div class="work"> <a href="assets/img/portfolio/camera1.jpg" rel="prettyPhoto[pp_gal]" title="the rooms in the farmhouse"> <img src="assets/img/portfolio/camera1-p.jpg" alt=""> </a>
                      <h4> Room 1</h4>
                    </div>
                  </li>
                    <li data-id="p-5" data-type="trullo" class="span1">
                      <div class="work"> <a href="assets/img/portfolio/camera2.jpg" rel="prettyPhoto[pp_gal]" title="the rooms in the farmhouse"> <img src="assets/img/portfolio/camera2-p.jpg" alt=""> </a>
                        <h4>Room 2</h4>
                      </div>
                    </li>
             <li data-id="p-12" data-type="dependance" class="span1">
               <div class="work"> <a href="assets/img/portfolio/camera3.jpg" rel="prettyPhoto[pp_gal]" title="the rooms in the farmhouse"> <img src="assets/img/portfolio/camera3-p.jpg" alt=""> </a>
                 <h4>Room 3</h4>
               </div>
             </li>
                  <li data-id="p-11" data-type="casette" class="span1">
                    <div class="work"> <a href="assets/img/portfolio/camera4.jpg" rel="prettyPhoto[pp_gal]" title="the rooms in the farmhouse"> <img src="assets/img/portfolio/camera4-p.jpg" alt=""> </a>
                      <h4>Room 4</h4>
                    </div>
                  </li>
                  <li data-id="p-11" data-type="casette" class="span1">
                    <div class="work"> <a href="assets/img/portfolio/camera5.jpg" rel="prettyPhoto[pp_gal]" title="the rooms in the farmhouse"> <img src="assets/img/portfolio/camera5-p.jpg" alt=""> </a>
                      <h4>Room 5</h4>
                    </div>
                  </li>
                </ul>
            </div>
        </div>
          <!-- Call To Action -->
        <div class="call-to-action container">
          <div class="row">
            <div class="call-to-action-text span12">
              <div class="ca-text">
                <p>Daily room rates</p>
              </div>
              <div class="ca-button"><a href="prenotazioni.php">Booking </a></div>
              
            </div>
          </div>
			
			<div id="slope-bl" data-id="8c73d3c9-df98-41f2-8b24-618e83ff232a" data-lang="en">             </div>
			
        </div>
        <div class="portfolio portfolio-page container">
          <div class="row">
            <div class="portfolio-navigator span12">
              <div class="portfolio-title">
                <h3> Types of rooms</h3>
              </div>
            </div>
          </div>
          <div class="row">
            <ul class="portfolio-img">
              <li data-id="p-1" data-type="camere" class="span3">
                <div class="work"> <img src="assets/img/portfolio/camera1-p.jpg" alt="">
                  <h4>Rooms in the farmhouse </h4>
                  <p><a href="camere1.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
              <li data-id="p-5" data-type="trullo" class="span3">
                <div class="work"> <img src="assets/img/portfolio/trullo1-p.jpg" alt="">
                  <h4>the "trullo"</h4>
                  <p><a href="trullo.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
              <li data-id="p-12" data-type="dependance" class="span3">
                <div class="work"> <img src="assets/img/portfolio/dependance1-p.jpg" alt="">
                  <h4>The guest houses</h4>
                  <p><a href="dependance.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
              <li data-id="p-11" data-type="casette" class="span3">
                <div class="work"> <img src="assets/img/portfolio/casette4-p.jpg" alt="">
                  <h4>The Quercia cottages </h4>
                  <p><a href="casette.php">CHARACTERISTICS ></a></p>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <!-- Footer -->
      
        <!-- Javascript -->

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>