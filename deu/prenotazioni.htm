
<!DOCTYPE html>
<html lang="en">

    <head>

        <meta charset="utf-8">
        <title>Masseria Torricella</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <meta name="author" content="">

        <!-- CSS -->
        <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Open+Sans:400italic,400">
        <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Droid+Sans">
        <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Lobster">
        <link rel="stylesheet" href="assets/bootstrap/css/bootstrap.min.css">
        <link rel="stylesheet" href="assets/prettyPhoto/css/prettyPhoto.css">
        <link rel="stylesheet" href="assets/css/flexslider.css">
        <link rel="stylesheet" href="assets/css/font-awesome.css">
        <link rel="stylesheet" href="assets/css/style.css">

        <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
        <!--[if lt IE 9]>
            <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
        <![endif]-->

        <!-- Favicon and touch icons -->
        <link rel="shortcut icon" href="assets/ico/favicon.ico">
        <link rel="apple-touch-icon-precomposed" sizes="144x144" href="assets/ico/apple-touch-icon-144-precomposed.png">
        <link rel="apple-touch-icon-precomposed" sizes="114x114" href="assets/ico/apple-touch-icon-114-precomposed.png">
        <link rel="apple-touch-icon-precomposed" sizes="72x72" href="assets/ico/apple-touch-icon-72-precomposed.png">
        <link rel="apple-touch-icon-precomposed" href="assets/ico/apple-touch-icon-57-precomposed.png">
        <style type="text/css">
        body,td,th {
	font-family: "Open Sans", Helvetica, Arial, sans-serif;
}
body {
	background-color: #FFFCEC;
	background-repeat: no-repeat;
}
        </style>
        <script type="text/javascript">
<!--
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_validateForm() { //v4.0
  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);
    if (val) { nm=val.name; if ((val=val.value)!="") {
      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');
        if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';
      } else if (test!='R') { num = parseFloat(val);
        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';
        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');
          min=test.substring(8,p); max=test.substring(p+1);
          if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';
    } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' obbligatorio.\n'; }
  } 
  
 errors+=test_privacy();
  
  if (errors) alert('Errori nella compilazione del modulo:\n'+errors);
  document.MM_returnValue = (errors == '');
}

//funzione checkbox privacy
// - il campo del check si deve chiamare check_privacy
// - prima dell'ultimo if in MM_validateForm bisogna aggiungere questa riga: errors+=test_privacy();

function test_privacy(){
if (!document.all.check_privacy.checked){ 
	vvv='- Per continuare devi autorizzare il trattamento dei dati personali.'
} 
else { 
	vvv=''; 
}
return vvv; 
}

//-->
</script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-XQ3WQSR0K6"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-XQ3WQSR0K6');
</script>
</head>

    <body>

        <!-- Header -->
        <div class="container">
            <div class="header row">
                <div class="span12">
                    <div class="navbar">
                        <div class="navbar-inner">
                            <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                                <span class="icon-bar"></span>
                            </a>
                            <h1>
                                <a class="brand"></a>
                            </h1>
                            <div class="nav-collapse collapse">
                              <ul class="nav pull-right">
                                <li class="current-page"> <a href="index.html">Home</a></li>
                                <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >La masseria<b class="caret"></b></a>
                                  <ul class="dropdown-menu">
                                    <li><a href="presentazione.htm">PRÄSENTATION</a></li>
                                    <li class="divider"></li>
                                    <li><a href="masseria.htm">DIE MASSERIA </a></li>
                                    <li class="divider"></li>
                                    <li><a href="storia.htm">GESCHICHTE UND URSPRÜNGE </a></li>
                                    <li class="divider"></li>
                                    <li><a href="posizione.htm">LAGE</a></li>
                                    <li class="divider"></li>
                                    <li><a href="dintorni.htm">UMGEBUNG</a></li>
                                  </ul>
                                </li>
                                <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >STRUKTUR<b class="caret"></b></a>
                                  <ul class="dropdown-menu">
                                    <li><a href="camere.htm">ZIMMER </a></li>
                                    <li class="divider"></li>
                                    <li><a href="tariffe.htm">PREISE ZIMMER </a></li>
                                    <li class="divider"></li>
                                    <li><a href="casevacanza.htm">VILLEN </a></li>
                                    <li class="divider"></li>
                                    <li><a href="tariffe2.htm">PREISE VILLEN </a></li>
                                    <li class="divider"></li>
                                    <li><a href="prenotazioni.htm">BUCHEN</a></li>
                                  </ul>
                                </li>
                                <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >UNSERE LEISTUNGEN<b class="caret"></b></a>
                                  <ul class="dropdown-menu">
                                    <li><a href="ristorante.htm">RESTAURANT </a></li>
                                    <li class="divider"></li>
                                    <li><a href="piscina.htm">POOL </a></li>
                                    <li class="divider"></li>
                                    <li><a href="servizi.htm">LEISTUNGEN </a></li>
                                    <li class="divider"></li>
                                    <li><a href="natura.htm">WALDGEBIET</a></li>
                                  </ul>
                                </li>
                                <li> <a href="https://www.masseriatorricellashop.it/it/">shop on line</a> </li>
                                <li> <a href="gallery.htm">PHOTOGALLERY</a></li>
                                <li class="dropdown"> <a href="#" class="dropdown-toggle" data-toggle="dropdown" >KONTAKTE<b class="caret"></b></a>
                                  <ul class="dropdown-menu">
                                    <li><a href="contatti.htm">Info</a></li>
                                    <li class="divider"></li>
                                    <li><a href="dovesiamo.htm">WO SIE UNS FINDEN</a></li>
                                  </ul>
                                </li>
                              </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Slider -->
    <div class="page-title">
      <div class="container">
        <div class="row">
          <div class="span12">
            <h3>Buchen <em></em></h3>
          </div>
        </div>
      </div>
    </div>
    <!-- Contact Us -->
        <div class="contact-us container">
            <div class="row">
                <div class="services-half-width-text span6">
                   
                 <table width="100%" border="0" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF" style="padding:15px;">
          <tr>
            <td>
              <form action="http://www.masseriatorricella.it/prenotacdosys.asp" method="post" style="margin:0px;" onsubmit="MM_validateForm('Arrivo','','R','Partenza','','R','camere','','RisNum','email','','RisEmail');return document.MM_returnValue">
              <div align="left">
                <h4><strong>Anfrage über Verfügbarkeit u/o Online-Buchung<br />  
                </strong></h4>
              </div>
            <br />
             <div style="background-color:#E5E0A6;padding:20px; text-align:left;">
               <table width="100%" border="0" cellspacing="5" cellpadding="0">
                 <tr>
                   <td width="50%"><strong>Data di arrivo*:</strong></td>
                   <td><input name="Arrivo" id="Arrivo" onclick='scwShow(this,event);' /></td>
                 </tr>
                 <tr>
                   <td><strong>Data di partenza*:</strong></td>
                   <td><input name="Partenza" id="Partenza" onclick='scwShow(this,event);' />                   </td>
                 </tr>
                 <tr>
                   <td>&nbsp;</td>
                   <td>&nbsp;</td>
                 </tr>
                 <tr>
                   <td>&nbsp;</td>
                   <td>&nbsp;</td>
                 </tr>
                 <tr>
                   <td><strong>Adulti:</strong></td>
                   <td><input name="Adulti" type="text" id="Adulti" size="5" /></td>
                 </tr>
                 <tr>
                   <td><strong>Bambini da 3 a 10 anni:</strong></td>
                   <td><input name="Bambini" type="text" id="Bambini" size="5" /></td>
                 </tr>
                 <tr>
                   <td valign="top"><strong>Numero camere*:</strong></td>
                   <td><input name="camere" type="text" id="camere" size="5" />
                     <br />
                     <br /></td>
                 </tr>
                 <tr>
                   <td colspan="2"><table width="100%">
                       <tr>
                         <td width="26%"><strong>
                           <label>
                           <input name="Scelta" type="radio" id="RadioGroup1_0" value="BB" checked="checked" />
                           bed and breakfast</label>
                         </strong></td>
                         <td width="24%"><strong>
                           <label>
                           <input type="radio" name="Scelta" value="MP" id="RadioGroup1_1" />
                           mezza pensione </label>
                         </strong></td>
                         <td width="24%"><strong>
                           <label>
                           <input type="radio" name="Scelta" value="PC" id="RadioGroup1_2" />
                           pensione completa </label>
                            </strong></td>
                            <td width="26%"><strong>
                           <label>
                           <input type="radio" name="Scelta" value="PC" id="RadioGroup1_3" />
                           formula residence </label>
                         </strong></td>
                        </tr>
                       
                       </table></td>
                  </tr>
                 <tr>
                   <td colspan="2"><strong><br />
                     Richieste speciali:<br />
                   </strong><textarea style="width:97%" name="Richieste" id="Richieste" cols="30" rows="5"></textarea></td>
                  </tr>
               </table>
             </div>
             <div align="left"><br />
               <strong>INFORMAZIONI PERSONALI</strong> </div>
             <div style="background-color:#E5E0A6;padding:20px; text-align:left;">
               <table width="100%" border="0" cellspacing="5" cellpadding="0">
                  <tr>
                    <td width="50%"><strong>Nome</strong></td>
                    <td><input name="Nome" type="text" id="Nome" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Cognome</strong></td>
                    <td><input name="Cognome" type="text" id="Cognome" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Indirizzo</strong></td>
                    <td><input name="Indirizzo" type="text" id="Indirizzo" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>CAP</strong></td>
                    <td><input name="CAP" type="text" id="CAP" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Città</strong></td>
                    <td><input name="Città" type="text" id="Città" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Telefono</strong></td>
                    <td><input name="Telefono" type="text" id="Telefono" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>Fax</strong></td>
                    <td><input name="Fax" type="text" id="Fax" style="width:97%" /></td>
                  </tr>
                  <tr>
                    <td><strong>E-mail*</strong></td>
                    <td><input name="email" type="text" id="email" style="width:97%" /></td>
                  </tr>
                </table>
             </div>
             
             <div align="left"><strong><br />
               Tutela della privacy:</strong><br />
               ai sensi della legge 196/03 sulla tutela dei dati personali, i dati inviati
               saranno utilizzati esclusivamente per attività legate ai servizi offerti dall'Azienda Agrituristica Masseria Torricella e non saranno per alcuna 
               ragione ceduti a terzi.
             </div>
             <table width="51%" border="0" cellspacing="5" cellpadding="0">
              <tr>
                <td width="26%" valign="top"><div align="left">
                        <strong>Autorizzo</strong>
                  <input name="check_privacy" type="checkbox" id="check_privacy" value="autorizzo_privacy" />
                  </td>
                <td width="51%"><div align="center"><br>
                  <input type="submit" name="button" id="button" value="Invia&gt;&gt;" />
                </div></td>
              </tr>
            </table>
             </form>             </td>
          </tr>
          
          <tr>
            <td>&nbsp;</td>
          </tr>
        </table>
              </div>
                <div class="services-half-width-text span6"><br>
                    <h4><strong>Anfrage über Verfügbarkeit u/o Buchung</h4>
                    <br><br><br>
                   
                  <p align="justify"><br>
                    <STRONG><U>Anfrage über  Verfügbarkeit u/o Buchung über Fax</U></STRONG><BR>
                    -Formular ausdrücken (die Funktion Drucken  des Browsers verwenden)<BR>
                  </p>
                    <p align="justify"><STRONG><U>Anfrage  über Verfügbarkeit u/o telefonische Buchung</U></STRONG><BR>
                      - Mobiltelefon:<strong> 333.3968173</strong> - Telefon: <strong>080.9309994</strong> <br><br>
                  </p>
                    <p>Um die Buchung (über Fax, E-Mail oder  Telefon) zu bestätigen, muss eine Anzahlung von 20% des Gesamtpreises durch  Banküberweisung auf untengenanntes Konto überwiesen werden. </p>
                    <p align="justify"><br>
                    </p>
                </div>
            </div>
        </div>
    <!-- Footer -->
        <footer>
            <div class="container">
              <div class="row">
                <div class="widget span3">
                  <h4>KONTAKTE</h4>
                  <p><i class="icon-map-marker"></i>S.P. Canale di Pirro, 19 70011 Alberobello (BA)</p>
                  <p><i class="icon-phone"></i> Cell. **********</p>
                  <p><i class="icon-envelope-alt"></i> Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                  <p>
                  <h4>&nbsp;</h4>
                  <h4>Realisierung:</h4>
                  <a href="http://www.acti-web.com">Actiweb</a>
                  </p>
                </div>
                <div class="widget span3">
                  <h4> Sprachen: </h4>
                  <div class="social span8"> <a class="ita" href="../index.html"></a> <a class="eng" href="../eng/index.html"></a> <a class="deu" href="index.html"></a> <a class="spa" href="../spa/index.html"></a> <a class="fra" href="../fra/index.html"></a></div><h4>&nbsp;</h4>
                  
                  <h4>PHOTOGALLERY</h4>
                    <div class="social span8"> <a class="gal1" href="gallery.htm"></a> <a class="gal2" href="gallery2.htm"></a> <a class="gal3" href="gallery3.htm"></a></div><h4>&nbsp;</h4>
                </div>
                <div class="widget span3"><iframe src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fmasseriatorricellaalberobello%2F&tabs=timeline&width=340&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId" width="340" height="500" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowTransparency="true"></iframe>
                </div>
                <div class="widget span3">
                  <iframe src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fmasseriatorricellaviverebio%2F&tabs=timeline&width=340&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId" width="340" height="500" style="border:none;overflow:hidden;margin-left:45px" scrolling="no" frameborder="0" allowTransparency="true"></iframe>
                </div>
              </div>
              <div class="footer-border"></div>
                <div class="row">
                    
                    
                    </div>
                </div>
            </div>
    </footer>
        <!-- Javascript -->
    <script src="assets/js/jquery-1.8.2.min.js"></script>
    <script src="assets/bootstrap/js/bootstrap.min.js"></script>
    <script src="assets/js/jquery.flexslider.js"></script>
    <script src="assets/prettyPhoto/js/jquery.prettyPhoto.js"></script>
    <script src="assets/js/scripts.js"></script>

    <!-- inizio banner policy cookie  -->
<link href="http://www.acti-web.com/privacy/stili.css" rel="stylesheet" type="text/css">
<iframe id='cookie_iframe' src="http://www.acti-web.com/privacy/privacy_global.php?s=masseriatorricella"></iframe>
<!-- fine banner policy cookie -->
    </body>

</html>

