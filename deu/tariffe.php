<?php
/**
 * Tariffe Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once '../includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Masseria Torricella',
    'page_description' => '',
    'page_keywords' => '',
    'current_page' => 'tariffe.htm'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

<div class="page-title">
      <div class="container"></div>
    </div>
    <!-- descrizione --><!-- gallery -->
     <div class="portfolio portfolio-page container">
       <div class="row">
         <div class="portfolio-navigator span12">
           <div class="portfolio-title">
             <h3>Alle Zimmer-Typologien</h3>
           </div>
         </div>
       </div>
       <div class="row">
         <ul class="portfolio-img">
           <li data-id="p-1" data-type="camere" class="span3">
             <div class="work"> <img src="assets/img/portfolio/camera1-p.jpg" alt="">
               <h4>Zimmer in der Masseria</h4>
               <p><a href="camere1.php">BESCHREIBUNG &gt;</a></p>
             </div>
           </li>
           <li data-id="p-5" data-type="trullo" class="span3">
             <div class="work"> <img src="assets/img/portfolio/trullo1-p.jpg" alt="">
               <h4>Der Trullo</h4>
               <p><a href="trullo.php">BESCHREIBUNG &gt;</a></p>
             </div>
           </li>
           <li data-id="p-12" data-type="dependance" class="span3">
             <div class="work"> <img src="assets/img/portfolio/dependance1-p.jpg" alt="">
               <h4>Die Dependancen</h4>
               <p><a href="dependance.php">BESCHREIBUNG &gt;</a></p>
             </div>
           </li>
           <li data-id="p-11" data-type="casette" class="span3">
             <div class="work"> <img src="assets/img/portfolio/casette4-p.jpg" alt="">
               <h4>Bungalows an der Eiche</h4>
               <p><a href="casette.php">BESCHREIBUNG &gt;</a></p>
             </div>
           </li>
         </ul>
       </div>
     </div>
     <!-- Footer -->
        
        <!-- Javascript -->

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>