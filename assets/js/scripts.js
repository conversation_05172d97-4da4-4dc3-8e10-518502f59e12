/*
    Mick mod menu 
*/
$(document).ready(function () {
  //jQuery('.dropdown-menu > li > a[href*=ristorante]').parent().after( "<li class='divider'></li><li><a href='spa.htm'>La SPA</a></li>" );
});
//sostituisce l'intero menu con quello del file _menu.htm
$(document).ready(function () {
  //jQuery(".nav-collapse.collapse").load("_menu.htm");
});

//tolgo la vecchia privacy e la sostituisco con la nuova
document.addEventListener("DOMContentLoaded", function () {
  var iframe = document.getElementById("cookie_iframe");
  iframe.remove();
  // Trova lo script originale tramite il suo src
  var scripts = document.querySelectorAll('script[type="text/javascript"]');
  var oldScript = null;

  scripts.forEach(function (script) {
    if (
      script.src ===
      "https://www.acti-web.org/privacy-v2/privacy_script.php?s=MASSERIATORRICELLAIT"
    ) {
      //oldScript = script;
      script.remove();

      console.log("tolto vecchi script privacy");
    }
  });

  // Crea un nuovo elemento script
  var newScript = document.createElement("script");
  newScript.type = "text/javascript";
  newScript.src =
    "https://www.acti-web.org/privacy-v3/privacy_script.php?s=masseriatorricella";

  // Aggiungi il nuovo script al DOM
  document.body.appendChild(newScript);
});

//elimino il form di contatto
window.onload = function () {
  console.log("Pagina completamente caricata. Rimozione dei form...");

  // Trova tutti gli elementi <form> nella pagina
  var forms = document.querySelectorAll("form");

  // Itera su tutti i form trovati
  forms.forEach(function (form) {
    // Verifica se l'attributo "action" corrisponde a "formcdosys.asp"
    if (form.getAttribute("action") === "formcdosys.asp") {
      // Rimuovi il form dal DOM
      form.remove();
      console.log("Form rimosso:", form);
    }
  });
};

/*
    Slider
*/
$(window).load(function () {
  $(".flexslider").flexslider({
    animation: "fade",
    controlNav: "thumbnails",
  });
});

/*
    Slider2
*/
$(window).load(function () {
  $(".flexslider2").flexslider({
    animation: "fade",
  });
});

/*
    Filterable portfolio
*/
jQuery(document).ready(function () {
  $clientsHolder = $("ul.portfolio-img");
  $clientsClone = $clientsHolder.clone();

  $(".filter-portfolio a").click(function (e) {
    e.preventDefault();
    $filterClass = $(this).attr("class");

    $(".filter-portfolio a").attr("id", "");
    $(this).attr("id", "active-imgs");

    if ($filterClass == "all") {
      $filters = $clientsClone.find("li");
    } else {
      $filters = $clientsClone.find("li[data-type~=" + $filterClass + "]");
    }

    $clientsHolder.quicksand($filters, { duration: 700 }, function () {
      $("a[rel^='prettyPhoto']").prettyPhoto({ social_tools: false });
    });
  });
});

/*
    Pretty Photo
*/
jQuery(document).ready(function () {
  $("a[rel^='prettyPhoto']").prettyPhoto({ social_tools: false });
});

/*
    Show latest tweets

jQuery(function($) {
    $(".show-tweets").tweet({
        username: "anli_zaimi",
        page: 1,
        count: 10,
        loading_text: "loading ..."
    }).bind("loaded", function() {
        var ul = $(this).find(".tweet_list");
        var ticker = function() {
            setTimeout(function() {
                ul.find('li:first').animate( {marginTop: '-4em'}, 500, function() {
                    $(this).detach().appendTo(ul).removeAttr('style');
                });
                ticker();
            }, 5000);
        };
        ticker();
    });
});
*/

/*
    Flickr feed
*/
$(document).ready(function () {
  $(".flickr-feed").jflickrfeed({
    limit: 8,
    qstrings: {
      id: "52617155@N08",
    },
    itemTemplate:
      '<li><a href="{{link}}" target="_blank"><img src="{{image_s}}" alt="{{title}}" /></a></li>',
  });
});

/*
    Google maps
*/
jQuery(document).ready(function () {
  var position = new google.maps.LatLng(45.067883, 7.687231);
  $(".map").gmap({
    center: position,
    zoom: 15,
    disableDefaultUI: true,
    callback: function () {
      var self = this;
      self.addMarker({ position: this.get("map").getCenter() });
    },
  });
});

/*
    Contact form
*/
jQuery(document).ready(function () {
  $(".contact-form form").submit(function () {
    $(".contact-form form .nameLabel").html("Name");
    $(".contact-form form .emailLabel").html("Email");
    $(".contact-form form .messageLabel").html("Message");

    var postdata = $(".contact-form form").serialize();
    $.ajax({
      type: "POST",
      url: "assets/sendmail.php",
      data: postdata,
      dataType: "json",
      success: function (json) {
        if (json.nameMessage != "") {
          $(".contact-form form .nameLabel").append(
            ' - <span class="violet" style="font-size: 13px; font-style: italic"> ' +
              json.nameMessage +
              "</span>"
          );
        }
        if (json.emailMessage != "") {
          $(".contact-form form .emailLabel").append(
            ' - <span class="violet" style="font-size: 13px; font-style: italic"> ' +
              json.emailMessage +
              "</span>"
          );
        }
        if (json.messageMessage != "") {
          $(".contact-form form .messageLabel").append(
            ' - <span class="violet" style="font-size: 13px; font-style: italic"> ' +
              json.messageMessage +
              "</span>"
          );
        }
        if (
          json.nameMessage == "" &&
          json.emailMessage == "" &&
          json.messageMessage == ""
        ) {
          $(".contact-form form").fadeOut("fast", function () {
            $(".contact-form").append(
              '<p><span class="violet">Thanks for contacting us!</span> We will get back to you very soon.</p>'
            );
          });
        }
      },
    });
    return false;
  });
});
