RewriteEngine On

# HTTPS and www redirect
RewriteCond %{HTTPS} off [OR]
RewriteCond %{HTTP_HOST} ^www\. [NC]
RewriteCond %{HTTP_HOST} ^(?:www\.)?(.+)$ [NC]
RewriteRule ^ https://%1%{REQUEST_URI} [L,NE,R=301]

# Template system support - redirect .htm to .php if .php exists
# This helps during migration period
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_URI} \.htm$
RewriteCond %{DOCUMENT_ROOT}%{REQUEST_URI}l -f
RewriteRule ^(.*)\.htm$ $1.html [L,R=301]

# Enable PHP error reporting for development (remove in production)
# php_flag display_errors on
# php_value error_reporting "E_ALL"
