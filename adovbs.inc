<%
'--------------------------------------------------------------------
' Microsoft ADO
'
' (c) 1996 Microsoft Corporation.  All Rights Reserved.
'
'
'
' ADO constants include file for VBScript
'
'--------------------------------------------------------------------

'---- CursorTypeEnum Values ----
Const adOpenForwardOnly = 0
Const adOpenKeyset = 1
Const adOpenDynamic = 2
Const adOpenStatic = 3

'---- CursorOptionEnum Values ----
Const adHoldRecords = &*********
Const adMovePrevious = &*********
Const adAddNew = &*********
Const adDelete = &*********
Const adUpdate = &*********
Const adBookmark = &*********
Const adApproxPosition = &*********
Const adUpdateBatch = &*********
Const adResync = &*********

'---- LockTypeEnum Values ----
Const adLockReadOnly = 1
Const adLockPessimistic = 2
Const adLockOptimistic = 3
Const adLockBatchOptimistic = 4

'---- CursorLocationEnum Values ----
Const adUseClient = 1
Const adUseServer = 2
Const adUseClientBatch = 3

'---- DataTypeEnum Values ----
Const adEmpty = 0
Const adTinyInt = 16
Const adSmallInt = 2
Const adInteger = 3
Const adBigInt = 20
Const adUnsignedTinyInt = 17
Const adUnsignedSmallInt = 18
Const adUnsignedInt = 19
Const adUnsignedBigInt = 21
Const adSingle = 4
Const adDouble = 5
Const adCurrency = 6
Const adDecimal = 14
Const adNumeric = 131
Const adBoolean = 11
Const adError = 10
Const adUserDefined = 132
Const adVariant = 12
Const adIDispatch = 9
Const adIUnknown = 13
Const adGUID = 72
Const adDate = 7
Const adDBDate = 133
Const adDBTime = 134
Const adDBTimeStamp = 135
Const adBSTR = 8
Const adChar = 129
Const adVarChar = 200
Const adLongVarChar = 201
Const adWChar = 130
Const adVarWChar = 202
Const adLongVarWChar = 203
Const adBinary = 128
Const adVarBinary = 204
Const adLongVarBinary = 205

'---- ConnectPromptEnum Values ----
Const adPromptAlways = 1
Const adPromptComplete = 2
Const adPromptCompleteRequired = 3
Const adPromptNever = 4

'---- ConnectModeEnum Values ----
Const adModeUnknown = 0
Const adModeRead = 1
Const adModeWrite = 2
Const adModeReadWrite = 3
Const adModeShareDenyRead = 4
Const adModeShareDenyWrite = 8
Const adModeShareExclusive = &Hc
Const adModeShareDenyNone = &H10

'---- IsolationLevelEnum Values ----
Const adXactUnspecified = &Hffffffff
Const adXactChaos = &*********
Const adXactReadUncommitted = &*********
Const adXactBrowse = &*********
Const adXactCursorStability = &*********
Const adXactReadCommitted = &*********
Const adXactRepeatableRead = &*********
Const adXactSerializable = &*********
Const adXactIsolated = &*********

'---- XactAttributeEnum Values ----
Const adXactPollAsync = 2
Const adXactPollSyncPhaseOne = 4
Const adXactCommitRetaining = &*********
Const adXactAbortRetaining = &*********
Const adXactAbortAsync = &*********

'---- FieldAttributeEnum Values ----
Const adFldBookmark = &*********
Const adFldMayDefer = &*********
Const adFldUpdatable = &*********
Const adFldUnknownUpdatable = &*********
Const adFldFixed = &*********
Const adFldIsNullable = &*********
Const adFldMayBeNull = &*********
Const adFldLong = &*********
Const adFldRowID = &*********
Const adFldRowVersion = &*********
Const adFldCacheDeferred = &*********

'---- EditModeEnum Values ----
Const adEditNone = &H0000
Const adEditInProgress = &H0001
Const adEditAdd = &H0002

'---- RecordStatusEnum Values ----
Const adRecOK = &********
Const adRecNew = &********
Const adRecModified = &********
Const adRecDeleted = &********
Const adRecUnmodified = &********
Const adRecInvalid = &********
Const adRecMultipleChanges = &********
Const adRecPendingChanges = &********
Const adRecCanceled = &********
Const adRecCantRelease = &********
Const adRecConcurrencyViolation = &********
Const adRecIntegrityViolation = &********
Const adRecMaxChangesExceeded = &********
Const adRecObjectOpen = &********
Const adRecOutOfMemory = &********
Const adRecPermissionDenied = &********
Const adRecSchemaViolation = &********
Const adRecDBDeleted = &********

'---- GetRowsOptionEnum Values ----
Const adGetRowsRest = -1

'---- PositionEnum Values ----
Const adPosUnknown = -1
Const adPosBOF = -2
Const adPosEOF = -3

'---- AffectEnum Values ----
Const adAffectCurrent = 1
Const adAffectGroup = 2
Const adAffectAll = 3

'---- FilterGroupEnum Values ----
Const adFilterNone = 0
Const adFilterPendingRecords = 1
Const adFilterAffectedRecords = 2
Const adFilterFetchedRecords = 3

'---- PropertyAttributesEnum Values ----
Const adPropNotSupported = &H0000
Const adPropRequired = &H0001
Const adPropOptional = &H0002
Const adPropRead = &H0200
Const adPropWrite = &H0400

'---- ErrorValueEnum Values ----
Const adErrInvalidArgument = &Hbb9
Const adErrNoCurrentRecord = &Hbcd
Const adErrIllegalOperation = &Hc93
Const adErrInTransaction = &Hcae
Const adErrFeatureNotAvailable = &Hcb3
Const adErrItemNotFound = &Hcc1
Const adErrObjectNotSet = &Hd5c
Const adErrDataConversion = &Hd5d
Const adErrObjectClosed = &He78
Const adErrObjectOpen = &He79
Const adErrProviderNotFound = &He7a
Const adErrBoundToCommand = &He7b

'---- ParameterAttributesEnum Values ----
Const adParamSigned = &H0010
Const adParamNullable = &H0040
Const adParamLong = &H0080

'---- ParameterDirectionEnum Values ----
Const adParamUnknown = &H0000
Const adParamInput = &H0001
Const adParamOutput = &H0002
Const adParamInputOutput = &H0003
Const adParamReturnValue = &H0004

'---- CommandTypeEnum Values ----
Const adCmdUnknown = 0
Const adCmdText = &H0001
Const adCmdTable = &H0002
Const adCmdStoredProc = &H0004

Const DSN="Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:/inetpub/vhosts/masseriatorricella.it/httpdocs/database/database.mdb"


%>
<% Function ToEng (DateIta)
  DateIta = Split(DateIta, "/")
  ToEng = DateIta(1) & "/" & DateIta(0) & "/" & DateIta(2)

End function%>

