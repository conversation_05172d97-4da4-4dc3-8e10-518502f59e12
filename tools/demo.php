<?php

/**
 * Template System Demonstration
 * Shows the benefits and capabilities of the new template system
 *
 * Uso: php tools/demo.php (poi visita demo.php nel browser)
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Initialize template system
require_once 'includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Template System Demo - Masseria Torricella',
    'page_description' => 'Demonstration of the new PHP template system benefits',
    'current_page' => 'demo.php'
];

// Content function
function renderContent()
{
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

    <div class="container">
        <div class="row">
            <div class="span12">
                <div class="page-header">
                    <h1>Template System Demonstration</h1>
                    <p class="lead">See how the new template system improves maintainability and reduces code duplication</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="span6">
                <h2>Before: Old System</h2>
                <div class="well">
                    <h4>Problems:</h4>
                    <ul>
                        <li><strong>5 copies</strong> of every page (one per language)</li>
                        <li><strong>Duplicated HTML</strong> in every file</li>
                        <li><strong>Manual updates</strong> required across all files</li>
                        <li><strong>Inconsistent navigation</strong> between languages</li>
                        <li><strong>Hard to maintain</strong> and error-prone</li>
                    </ul>

                    <h4>File Count Example:</h4>
                    <ul>
                        <li>index.html (Italian)</li>
                        <li>eng/index.html (English)</li>
                        <li>deu/index.html (German)</li>
                        <li>spa/index.html (Spanish)</li>
                        <li>fra/index.html (French)</li>
                    </ul>
                    <p><strong>Total: 5 files</strong> with 90% duplicated code</p>
                </div>
            </div>

            <div class="span6">
                <h2>After: New Template System</h2>
                <div class="well well-success">
                    <h4>Benefits:</h4>
                    <ul>
                        <li><strong>1 template file</strong> serves all languages</li>
                        <li><strong>Centralized layout</strong> management</li>
                        <li><strong>Single update</strong> affects all languages</li>
                        <li><strong>Consistent navigation</strong> automatically</li>
                        <li><strong>Easy to maintain</strong> and extend</li>
                    </ul>

                    <h4>File Structure:</h4>
                    <ul>
                        <li>index.php (serves all languages)</li>
                        <li>includes/header.php</li>
                        <li>includes/navigation.php</li>
                        <li>includes/footer.php</li>
                        <li>includes/config.php</li>
                    </ul>
                    <p><strong>Total: 5 files</strong> with 0% code duplication</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="span12">
                <h2>System Information</h2>
                <table class="table table-striped">
                    <tr>
                        <th>Current Language</th>
                        <td><?php echo getCurrentLanguage(); ?></td>
                    </tr>
                    <tr>
                        <th>Asset Path</th>
                        <td><?php echo getAssetPath(); ?></td>
                    </tr>
                    <tr>
                        <th>Available Languages</th>
                        <td>
                            <?php
                            global $languages;
                            foreach ($languages as $code => $lang) {
                                echo $lang['name'] . ' (' . $code . ') ';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Site Title</th>
                        <td><?php echo $GLOBALS['site_config']['title']; ?></td>
                    </tr>
                    <tr>
                        <th>Contact Email</th>
                        <td><?php echo $GLOBALS['site_config']['contact_email']; ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row">
            <div class="span12">
                <h2>Code Comparison</h2>

                <h3>Old Way (HTML)</h3>
                <pre class="prettyprint">
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;Masseria Torricella&lt;/title&gt;
    &lt;!-- 50+ lines of repeated head content --&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;!-- 100+ lines of repeated navigation --&gt;
    
    &lt;!-- Actual page content (only unique part) --&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Page Content&lt;/h1&gt;
    &lt;/div&gt;
    
    &lt;!-- 150+ lines of repeated footer --&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>

                <h3>New Way (PHP Template)</h3>
                <pre class="prettyprint">
&lt;?php
require_once 'includes/init.php';

$page_config = [
    'page_title' =&gt; 'Your Page Title',
    'current_page' =&gt; 'yourpage.htm'
];

function renderContent() {
?&gt;
    &lt;!-- Only the unique page content --&gt;
    &lt;div class="container"&gt;
        &lt;h1&gt;Page Content&lt;/h1&gt;
    &lt;/div&gt;
&lt;?php
}

renderPage('renderContent', $page_config);
?&gt;
</pre>
            </div>
        </div>

        <div class="row">
            <div class="span12">
                <h2>Next Steps</h2>
                <div class="alert alert-info">
                    <h4>Ready to migrate?</h4>
                    <ol>
                        <li>Test the template system with the demo pages (index.php, presentazione.php)</li>
                        <li>Use the migration helper to convert existing HTML files</li>
                        <li>Update internal links to point to new .php files</li>
                        <li>Test all language versions</li>
                        <li>Remove old .htm files after successful migration</li>
                    </ol>

                    <p><strong>Estimated time savings:</strong> 80% reduction in maintenance time</p>
                    <p><strong>Code reduction:</strong> 90% less duplicated HTML</p>
                </div>
            </div>
        </div>
    </div>

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>