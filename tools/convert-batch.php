<?php

/**
 * Script per conversione in batch di file HTML
 * Converte tutti i file .htm di una directory
 *
 * Uso: php tools/convert-batch.php
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// Includi il migration helper
require_once 'includes/migration-helper.php';

echo "🚀 Avvio conversione in batch...\n\n";

// Converti tutti i file .htm nella directory principale (italiano)
echo "=== Conversione file italiani ===\n";
$converted_it = MigrationHelper::batchConvert('.', '*.htm');
echo "Convertiti " . count($converted_it) . " file italiani\n\n";

// Converti tutti i file .htm nella directory inglese
echo "=== Conversione file inglesi ===\n";
if (is_dir('eng')) {
    $converted_en = MigrationHelper::batchConvert('eng', '*.htm');
    echo "Convertiti " . count($converted_en) . " file inglesi\n\n";
}

// Converti tutti i file .htm nella directory tedesca
echo "=== Conversione file tedeschi ===\n";
if (is_dir('deu')) {
    $converted_de = MigrationHelper::batchConvert('deu', '*.htm');
    echo "Convertiti " . count($converted_de) . " file tedeschi\n\n";
}

// Converti tutti i file .htm nella directory spagnola
echo "=== Conversione file spagnoli ===\n";
if (is_dir('spa')) {
    $converted_es = MigrationHelper::batchConvert('spa', '*.htm');
    echo "Convertiti " . count($converted_es) . " file spagnoli\n\n";
}

// Converti tutti i file .htm nella directory francese
echo "=== Conversione file francesi ===\n";
if (is_dir('fra')) {
    $converted_fr = MigrationHelper::batchConvert('fra', '*.htm');
    echo "Convertiti " . count($converted_fr) . " file francesi\n\n";
}

echo "✅ Conversione batch completata!\n";
echo "Ricorda di testare i file convertiti prima di rimuovere gli originali.\n";
