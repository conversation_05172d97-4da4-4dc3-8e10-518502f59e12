<?php
/**
 * Script per testare la rimozione automatica del banner cookie durante la conversione
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON><PERSON>di il migration helper
require_once 'includes/migration-helper.php';

echo "🧪 TEST RIMOZIONE AUTOMATICA BANNER COOKIE\n";
echo "==========================================\n\n";

// Testiamo con un file che ha il banner cookie
$test_files = [
    'posizione.htm',
    'servizi.htm',
    'ristorante.htm'
];

foreach ($test_files as $htm_file) {
    if (!file_exists($htm_file)) {
        echo "⚠️  File $htm_file non trovato, saltando...\n";
        continue;
    }
    
    echo "🔄 Testando conversione di: $htm_file\n";
    
    // Controlla se il file originale ha il banner cookie
    $original_content = file_get_contents($htm_file);
    $has_cookie_banner = (
        strpos($original_content, 'inizio banner policy cookie') !== false ||
        strpos($original_content, 'acti-web.com/privacy/stili.css') !== false
    );
    
    if ($has_cookie_banner) {
        echo "   📍 Banner cookie trovato nel file originale\n";
    } else {
        echo "   ℹ️  Nessun banner cookie nel file originale\n";
    }
    
    try {
        // Converti il file
        $php_file = str_replace('.htm', '.php', $htm_file);
        $output_file = MigrationHelper::convertHtmlToTemplate($htm_file, $php_file);
        
        // Controlla se il file convertito ha ancora il banner
        $converted_content = file_get_contents($output_file);
        $still_has_banner = (
            strpos($converted_content, 'inizio banner policy cookie') !== false ||
            strpos($converted_content, 'acti-web.com/privacy/stili.css') !== false
        );
        
        if ($has_cookie_banner && !$still_has_banner) {
            echo "   ✅ Banner cookie rimosso automaticamente!\n";
        } elseif (!$has_cookie_banner && !$still_has_banner) {
            echo "   ✅ Conversione completata (nessun banner da rimuovere)\n";
        } elseif ($still_has_banner) {
            echo "   ❌ Banner cookie ancora presente nel file convertito\n";
        }
        
        // Verifica sintassi
        $syntax_check = shell_exec("php -l $output_file 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "   ✅ Sintassi PHP valida\n";
        } else {
            echo "   ❌ Errori di sintassi\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Errore durante la conversione: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🎉 Test completato!\n";
echo "💡 Il migration helper ora rimuove automaticamente i banner cookie durante la conversione.\n";
?>
