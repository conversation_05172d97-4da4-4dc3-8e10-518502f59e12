<?php
/**
 * Script di test per verificare che tutti gli strumenti funzionino
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

echo "🧪 TEST DEGLI STRUMENTI DI MIGRAZIONE\n";
echo "=====================================\n\n";

// Test 1: Verifica che i file di template esistano
echo "1. 🔍 Verifica file di template...\n";
$template_files = [
    'includes/config.php',
    'includes/header.php',
    'includes/navigation.php',
    'includes/footer.php',
    'includes/init.php',
    'includes/migration-helper.php'
];

$all_ok = true;
foreach ($template_files as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file\n";
    } else {
        echo "   ❌ $file (MANCANTE)\n";
        $all_ok = false;
    }
}

// Test 2: Verifica sintassi PHP dei file di template
echo "\n2. 🔍 Verifica sintassi PHP...\n";
foreach ($template_files as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✅ $file (sintassi OK)\n";
        } else {
            echo "   ❌ $file (errori di sintassi)\n";
            $all_ok = false;
        }
    }
}

// Test 3: Verifica strumenti di migrazione
echo "\n3. 🔍 Verifica strumenti di migrazione...\n";
$tool_files = [
    'tools/convert-single.php',
    'tools/convert-batch.php',
    'tools/convert-selective.php',
    'tools/clean-converted-files.php',
    'tools/reconvert-presentazione.php',
    'tools/demo.php',
    'tools/run.php',
    'tools/menu.php'
];

foreach ($tool_files as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file\n";
    } else {
        echo "   ❌ $file (MANCANTE)\n";
        $all_ok = false;
    }
}

// Test 4: Conta file HTML e PHP
echo "\n4. 📊 Statistiche file...\n";
$htm_files = glob('*.htm');
$php_files = glob('*.php');

// Escludi file di utilità
$utility_files = ['demo.php'];
$content_php_files = array_diff($php_files, $utility_files);

echo "   📁 File .htm: " . count($htm_files) . "\n";
echo "   📁 File .php (contenuto): " . count($content_php_files) . "\n";
echo "   📈 Progresso conversione: " . (count($htm_files) > 0 ? round((count($content_php_files) / count($htm_files)) * 100, 1) : 0) . "%\n";

// Test 5: Test del sistema run.php
echo "\n5. 🧪 Test script run.php...\n";
$test_output = shell_exec("php tools/run.php 7 2>&1");
if (strpos($test_output, 'STATO ATTUALE DEL PROGETTO') !== false) {
    echo "   ✅ run.php funziona correttamente\n";
} else {
    echo "   ❌ run.php ha problemi\n";
    $all_ok = false;
}

// Risultato finale
echo "\n" . str_repeat("=", 50) . "\n";
if ($all_ok) {
    echo "🎉 TUTTI I TEST SUPERATI!\n";
    echo "✅ Il sistema di template è pronto per l'uso.\n\n";
    
    echo "🚀 PROSSIMI PASSI:\n";
    echo "1. Usa 'php tools/run.php 7' per vedere lo stato\n";
    echo "2. Usa 'php tools/run.php 1' per convertire un file\n";
    echo "3. Usa 'php tools/menu.php' per il menu interattivo\n";
    echo "4. Visita index.php nel browser per testare il template\n";
} else {
    echo "❌ ALCUNI TEST FALLITI!\n";
    echo "⚠️  Controlla i file mancanti o con errori sopra.\n";
}

echo "\n💡 Per aiuto: php tools/run.php 8\n";
?>
