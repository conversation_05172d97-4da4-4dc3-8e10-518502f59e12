<?php
/**
 * Script per rimuovere il banner delle policy cookie dai file PHP già convertiti
 * 
 * Uso: php tools/remove-cookie-banner.php
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

echo "🍪 RIMOZIONE BANNER POLICY COOKIE\n";
echo "=================================\n\n";

/**
 * Rimuove il banner delle policy cookie da un file PHP
 */
function removeCookieBannerFromFile($file_path) {
    if (!file_exists($file_path)) {
        echo "❌ File non trovato: $file_path\n";
        return false;
    }
    
    $content = file_get_contents($file_path);
    $original_content = $content;
    
    // Pattern per rimuovere il banner delle policy cookie
    $patterns = [
        // Pattern 1: Banner completo con commenti
        '#<!-- inizio banner policy cookie\s*-->.*?<!-- fine banner policy cookie\s*-->#s',
        
        // Pattern 2: Solo CSS link e iframe
        '#<link[^>]*acti-web\.com/privacy/stili\.css[^>]*>.*?<iframe[^>]*acti-web\.com/privacy/privacy_global\.php[^>]*></iframe>#s',
        
        // Pattern 3: Componenti individuali
        '#<link[^>]*href=["\']http://www\.acti-web\.com/privacy/stili\.css["\'][^>]*>\s*#',
        '#<iframe[^>]*src=["\']http://www\.acti-web\.com/privacy/privacy_global\.php[^"\']*["\'][^>]*></iframe>\s*#',
        
        // Pattern 4: Solo commenti
        '#<!-- inizio banner policy cookie\s*-->\s*#',
        '#<!-- fine banner policy cookie\s*-->\s*#'
    ];
    
    foreach ($patterns as $pattern) {
        $content = preg_replace($pattern, '', $content);
    }
    
    // Pulisci righe vuote eccessive
    $content = preg_replace('#\n\s*\n\s*\n#', "\n\n", $content);
    
    // Controlla se sono state fatte modifiche
    if ($content !== $original_content) {
        file_put_contents($file_path, $content);
        echo "✅ Banner rimosso da: $file_path\n";
        return true;
    } else {
        echo "ℹ️  Nessun banner trovato in: $file_path\n";
        return false;
    }
}

/**
 * Mostra anteprima delle modifiche
 */
function previewCookieBannerRemoval($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $content = file_get_contents($file_path);
    
    // Cerca il banner delle policy cookie
    $cookie_patterns = [
        'inizio banner policy cookie',
        'acti-web.com/privacy/stili.css',
        'acti-web.com/privacy/privacy_global.php',
        'fine banner policy cookie'
    ];
    
    $found_banner = false;
    $lines = explode("\n", $content);
    $banner_lines = [];
    
    foreach ($lines as $line_num => $line) {
        foreach ($cookie_patterns as $pattern) {
            if (stripos($line, $pattern) !== false) {
                $found_banner = true;
                $banner_lines[] = ($line_num + 1) . ": " . trim($line);
            }
        }
    }
    
    if ($found_banner) {
        echo "\n📄 BANNER TROVATO IN: $file_path\n";
        echo str_repeat("-", 50) . "\n";
        foreach ($banner_lines as $banner_line) {
            echo "  $banner_line\n";
        }
        return true;
    }
    
    return false;
}

// Script principale
$php_files = glob('*.php');
$utility_files = ['demo.php'];
$files_to_check = array_diff($php_files, $utility_files);

if (empty($files_to_check)) {
    echo "ℹ️  Nessun file PHP da controllare trovato.\n";
    exit;
}

echo "📁 File da controllare:\n";
foreach ($files_to_check as $file) {
    echo "  - $file\n";
}

echo "\n🔍 RICERCA BANNER POLICY COOKIE\n";
echo "===============================\n";

$files_with_banner = [];
foreach ($files_to_check as $file) {
    if (previewCookieBannerRemoval($file)) {
        $files_with_banner[] = $file;
    }
}

if (empty($files_with_banner)) {
    echo "\n✅ Nessun banner delle policy cookie trovato!\n";
    echo "💡 Tutti i file sono già puliti.\n";
    exit;
}

echo "\n🚀 RIMOZIONE BANNER\n";
echo "===================\n";

$removed_count = 0;
foreach ($files_with_banner as $file) {
    if (removeCookieBannerFromFile($file)) {
        $removed_count++;
    }
}

echo "\n📊 RIEPILOGO\n";
echo "============\n";
echo "File controllati: " . count($files_to_check) . "\n";
echo "File con banner: " . count($files_with_banner) . "\n";
echo "Banner rimossi: $removed_count\n";

if ($removed_count > 0) {
    echo "\n✅ Rimozione completata!\n";
    echo "💡 I banner delle policy cookie sono stati rimossi da tutti i file.\n";
    echo "🔧 Il nuovo sistema di template gestirà i cookie tramite footer.php\n";
}
?>
