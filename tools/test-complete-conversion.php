<?php
/**
 * Script per testare tutte le funzionalità di conversione
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON><PERSON>di il migration helper
require_once 'includes/migration-helper.php';

echo "🧪 TEST COMPLETO FUNZIONALITÀ CONVERSIONE\n";
echo "=========================================\n\n";

/**
 * Testa un file di conversione completo
 */
function testCompleteConversion($htm_file) {
    echo "🔍 Testando: $htm_file\n";
    
    if (!file_exists($htm_file)) {
        echo "   ❌ File non trovato\n\n";
        return false;
    }
    
    $original_content = file_get_contents($htm_file);
    
    // Analisi file originale
    $has_cookie_banner = strpos($original_content, 'acti-web.com/privacy') !== false;
    $htm_links = preg_match_all('#href=["\'][^"\']*?\.htm[l]?["\']#i', $original_content);
    $asset_paths = preg_match_all('#(?:src|href)=["\'][^"\']*?assets/[^"\']*?["\']#i', $original_content);
    
    echo "   📊 Analisi file originale:\n";
    echo "      🍪 Banner cookie: " . ($has_cookie_banner ? "SÌ" : "NO") . "\n";
    echo "      🔗 Link .htm/.html: $htm_links\n";
    echo "      🖼️  Percorsi asset: $asset_paths\n";
    
    try {
        // Converti il file
        $php_file = str_replace('.htm', '.php', $htm_file);
        $output_file = MigrationHelper::convertHtmlToTemplate($htm_file, $php_file);
        
        $converted_content = file_get_contents($output_file);
        
        // Analisi file convertito
        $still_has_banner = strpos($converted_content, 'acti-web.com/privacy') !== false;
        $php_links = preg_match_all('#href=["\'][^"\']*?\.php["\']#i', $converted_content);
        $back_paths = preg_match_all('#(?:src|href)=["\'][^"\']*?\.\./[^"\']*?["\']#i', $converted_content);
        $correct_include = strpos($converted_content, '../includes/init.php') !== false || 
                          strpos($converted_content, 'includes/init.php') !== false;
        
        echo "   📊 Analisi file convertito:\n";
        echo "      🍪 Banner cookie rimosso: " . ($has_cookie_banner && !$still_has_banner ? "✅" : ($has_cookie_banner ? "❌" : "N/A")) . "\n";
        echo "      🔗 Link convertiti a .php: $php_links\n";
        echo "      🖼️  Percorsi con ../: $back_paths\n";
        echo "      📁 Include corretto: " . ($correct_include ? "✅" : "❌") . "\n";
        
        // Verifica sintassi
        $syntax_check = shell_exec("php -l $output_file 2>&1");
        $syntax_ok = strpos($syntax_check, 'No syntax errors') !== false;
        echo "      ✅ Sintassi PHP: " . ($syntax_ok ? "✅" : "❌") . "\n";
        
        // Calcola punteggio
        $score = 0;
        $max_score = 0;
        
        if ($has_cookie_banner) {
            $max_score++;
            if (!$still_has_banner) $score++;
        }
        
        if ($htm_links > 0) {
            $max_score++;
            if ($php_links > 0) $score++;
        }
        
        if ($asset_paths > 0 && dirname($htm_file) !== '.') {
            $max_score++;
            if ($back_paths > 0) $score++;
        }
        
        $max_score++; // Include
        if ($correct_include) $score++;
        
        $max_score++; // Sintassi
        if ($syntax_ok) $score++;
        
        $percentage = $max_score > 0 ? round(($score / $max_score) * 100, 1) : 100;
        echo "   📈 Punteggio: $score/$max_score ($percentage%)\n";
        
        return $percentage >= 80; // Considera successo se >= 80%
        
    } catch (Exception $e) {
        echo "   ❌ Errore: " . $e->getMessage() . "\n";
        return false;
    }
    
    echo "\n";
}

// File di test da diverse cartelle
$test_files = [
    // Root
    'presentazione.htm',
    'contatti.htm',
    
    // Sottocartelle
    'eng/index.htm',
    'deu/masseria.htm',
    'spa/servizi.htm',
    'fra/gallery.htm'
];

echo "🚀 Iniziando test completo...\n\n";

$total_tests = 0;
$successful_tests = 0;

foreach ($test_files as $test_file) {
    $total_tests++;
    if (testCompleteConversion($test_file)) {
        $successful_tests++;
    }
}

echo "🎉 RISULTATI FINALI\n";
echo "===================\n";
echo "Test eseguiti: $total_tests\n";
echo "Test riusciti: $successful_tests\n";
echo "Tasso di successo: " . round(($successful_tests / $total_tests) * 100, 1) . "%\n\n";

if ($successful_tests === $total_tests) {
    echo "✅ TUTTI I TEST SUPERATI!\n";
    echo "💡 Il sistema di conversione funziona perfettamente:\n";
    echo "   🔗 Conversione link interni .htm → .php\n";
    echo "   🖼️  Conversione percorsi asset per sottocartelle\n";
    echo "   🍪 Rimozione automatica banner cookie\n";
    echo "   📁 Percorsi include corretti per ogni posizione\n";
    echo "   ✅ Sintassi PHP valida\n";
} else {
    echo "⚠️  Alcuni test falliti. Controlla i dettagli sopra.\n";
}
?>
