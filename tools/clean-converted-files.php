<?php

/**
 * Script per pulire i file PHP già convertiti
 * Rimuove tag di chiusura inutili all'inizio del contenuto
 *
 * Uso: php tools/clean-converted-files.php
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// Includi il migration helper aggiornato
require_once 'includes/migration-helper.php';

/**
 * Pulisce il contenuto di un file PHP già convertito
 */
function cleanConvertedPhpFile($file_path)
{
    if (!file_exists($file_path)) {
        echo "❌ File non trovato: $file_path\n";
        return false;
    }

    $content = file_get_contents($file_path);

    // Pattern per trovare la funzione renderContent
    $pattern = '/(function renderContent\(\) \{[^}]*\?>)(.*?)(<\?php\s*\})/s';

    if (preg_match($pattern, $content, $matches)) {
        $before = $matches[1];
        $html_content = $matches[2];
        $after = $matches[3];

        echo "🔍 Analizzando: $file_path\n";

        // Mostra il contenuto problematico se presente
        if (preg_match('/^\s*(<\/div>\s*)+/', $html_content)) {
            echo "⚠️  Trovati tag di chiusura inutili all'inizio\n";

            // Pulisci il contenuto HTML
            $cleaned_html = cleanHtmlContent($html_content);

            // Ricostruisci il file
            $new_content = $before . $cleaned_html . $after;

            // Salva il file pulito
            file_put_contents($file_path, $new_content);

            echo "✅ File pulito: $file_path\n";
            return true;
        } else {
            echo "✅ File già pulito: $file_path\n";
            return true;
        }
    } else {
        echo "⚠️  Struttura non riconosciuta in: $file_path\n";
        return false;
    }
}

/**
 * Pulisce il contenuto HTML estratto
 */
function cleanHtmlContent($html_content)
{
    // Rimuovi tag di chiusura all'inizio
    $html_content = preg_replace('/^\s*(<\/div>\s*)+/', '', $html_content);

    // Rimuovi tag di apertura vuoti alla fine
    $html_content = preg_replace('/(<div[^>]*>\s*)+\s*$/', '', $html_content);

    // Rimuovi container div vuoti all'inizio
    $html_content = preg_replace('/^\s*<div[^>]*>\s*<\/div>\s*/', '', $html_content);

    // Rimuovi tag di chiusura isolati all'inizio
    $html_content = preg_replace('/^\s*<\/(div|span|p|h[1-6]|section|article)[^>]*>\s*/', '', $html_content);

    // Rimuovi righe vuote multiple
    $html_content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $html_content);

    // Rimuovi spazi iniziali e finali
    $html_content = trim($html_content);

    return $html_content;
}

/**
 * Mostra anteprima delle modifiche senza applicarle
 */
function previewCleanup($file_path)
{
    if (!file_exists($file_path)) {
        return false;
    }

    $content = file_get_contents($file_path);
    $pattern = '/(function renderContent\(\) \{[^}]*\?>)(.*?)(<\?php\s*\})/s';

    if (preg_match($pattern, $content, $matches)) {
        $html_content = $matches[2];

        echo "\n📄 ANTEPRIMA: $file_path\n";
        echo str_repeat("=", 50) . "\n";

        // Mostra prime righe del contenuto attuale
        $lines = explode("\n", $html_content);
        echo "CONTENUTO ATTUALE (prime 5 righe):\n";
        for ($i = 0; $i < min(5, count($lines)); $i++) {
            echo ($i + 1) . ": " . trim($lines[$i]) . "\n";
        }

        // Mostra contenuto dopo pulizia
        $cleaned = cleanHtmlContent($html_content);
        $cleaned_lines = explode("\n", $cleaned);
        echo "\nCONTENUTO DOPO PULIZIA (prime 5 righe):\n";
        for ($i = 0; $i < min(5, count($cleaned_lines)); $i++) {
            echo ($i + 1) . ": " . trim($cleaned_lines[$i]) . "\n";
        }

        return true;
    }

    return false;
}

// Script principale
echo "🧹 PULIZIA FILE PHP CONVERTITI\n";
echo "==============================\n\n";

// Trova tutti i file PHP (esclusi gli script di utilità)
$php_files = glob('*.php');
$utility_files = ['demo.php', 'convert-single.php', 'convert-batch.php', 'convert-selective.php', 'clean-converted-files.php'];

$files_to_clean = array_diff($php_files, $utility_files);

if (empty($files_to_clean)) {
    echo "ℹ️  Nessun file PHP da pulire trovato.\n";
    exit;
}

echo "📁 File trovati da analizzare:\n";
foreach ($files_to_clean as $file) {
    echo "  - $file\n";
}

echo "\n🔍 ANTEPRIMA DELLE MODIFICHE\n";
echo "============================\n";

// Mostra anteprima per ogni file
foreach ($files_to_clean as $file) {
    previewCleanup($file);
}

echo "\n🚀 APPLICAZIONE DELLE MODIFICHE\n";
echo "===============================\n";

$cleaned_count = 0;
$error_count = 0;

foreach ($files_to_clean as $file) {
    if (cleanConvertedPhpFile($file)) {
        $cleaned_count++;
    } else {
        $error_count++;
    }
}

echo "\n📊 RIEPILOGO\n";
echo "============\n";
echo "File processati: " . count($files_to_clean) . "\n";
echo "File puliti: $cleaned_count\n";
echo "Errori: $error_count\n";

if ($cleaned_count > 0) {
    echo "\n✅ Pulizia completata! I file sono ora pronti per l'uso.\n";
    echo "💡 Ricorda di testare i file nel browser per verificare che tutto funzioni correttamente.\n";
}
