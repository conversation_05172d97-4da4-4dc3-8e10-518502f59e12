<?php

/**
 * Script di utilità generale per eseguire gli strumenti di migrazione
 * Fornisce un menu interattivo per scegliere quale strumento usare
 *
 * Uso: php tools/run.php [numero_opzione]
 * Esempio: php tools/run.php 7  (per mostrare lo stato)
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Funzione per leggere input utente
function getUserInput($prompt)
{
    echo $prompt;
    $handle = fopen("php://stdin", "r");
    $input = trim(fgets($handle));
    fclose($handle);
    return $input;
}

// Controlla se è stato passato un argomento da riga di comando
$choice = isset($argv[1]) ? $argv[1] : null;

// Menu principale
function showMenu()
{
    echo "\n🛠️  STRUMENTI DI MIGRAZIONE TEMPLATE SYSTEM\n";
    echo "==========================================\n\n";

    echo "Scegli uno strumento:\n\n";
    echo "1. 📄 Converti un singolo file HTML\n";
    echo "2. 📦 Conversione in batch di tutti i file .htm\n";
    echo "3. 🎯 Conversione selettiva con controlli avanzati\n";
    echo "4. 🧹 Pulisci file PHP già convertiti (avanzato)\n";
    echo "5. 🔧 Pulisci file PHP (semplice e veloce)\n";
    echo "6. 🔄 Riconverti presentazione.htm\n";
    echo "7. 🎪 Genera demo del sistema\n";
    echo "8. 📊 Mostra stato attuale del progetto\n";
    echo "9. ❓ Mostra aiuto\n";
    echo "0. 🚪 Esci\n\n";
}

// Se non è stato passato un argomento, chiedi all'utente
if ($choice === null) {
    showMenu();
    $choice = getUserInput("Inserisci la tua scelta (0-8): ");
}

// Esegui l'azione scelta
executeChoice($choice);

// Funzione per eseguire l'azione scelta
function executeChoice($choice)
{
    switch ($choice) {
        case '1':
            echo "\n🚀 Eseguendo conversione singola...\n";
            include 'tools/convert-single.php';
            break;

        case '2':
            echo "\n🚀 Eseguendo conversione in batch...\n";
            echo "⚠️  Questo convertirà TUTTI i file .htm.\n";
            include 'tools/convert-batch.php';
            break;

        case '3':
            echo "\n🚀 Eseguendo conversione selettiva...\n";
            include 'tools/convert-selective.php';
            break;

        case '4':
            echo "\n🚀 Eseguendo pulizia file convertiti (avanzato)...\n";
            include 'tools/clean-converted-files.php';
            break;

        case '5':
            echo "\n🚀 Eseguendo pulizia semplice e veloce...\n";
            include 'tools/clean-files-simple.php';
            break;

        case '6':
            echo "\n🚀 Riconvertendo presentazione.htm...\n";
            include 'tools/reconvert-presentazione.php';
            break;

        case '7':
            echo "\n🚀 Generando demo...\n";
            include 'tools/demo.php';
            echo "✅ Demo generato! Visita demo.php nel browser.\n";
            break;

        case '8':
            showProjectStatus();
            break;

        case '9':
            showHelp();
            break;

        case '0':
            echo "\n👋 Arrivederci!\n";
            exit(0);

        default:
            echo "\n❌ Scelta non valida! Inserisci un numero da 0 a 9.\n";
            echo "Uso: php tools/run.php [0-9]\n";
            echo "Oppure: php tools/run.php (per menu interattivo)\n";
            exit(1);
    }
}

/**
 * Mostra lo stato attuale del progetto
 */
function showProjectStatus()
{
    echo "📊 STATO ATTUALE DEL PROGETTO\n";
    echo "=============================\n\n";

    // Conta file HTML originali
    $htm_files = glob('*.htm');
    $htm_count = count($htm_files);

    // Conta file PHP convertiti
    $php_files = glob('*.php');
    $utility_files = ['demo.php'];
    $content_php_files = array_diff($php_files, $utility_files);
    $php_count = count($content_php_files);

    // Conta file per lingua
    $languages = ['eng', 'deu', 'spa', 'fra'];
    $lang_stats = [];

    foreach ($languages as $lang) {
        if (is_dir($lang)) {
            $lang_htm = glob("$lang/*.htm");
            $lang_php = glob("$lang/*.php");
            $lang_stats[$lang] = [
                'htm' => count($lang_htm),
                'php' => count($lang_php)
            ];
        }
    }

    echo "📁 FILE NELLA ROOT (Italiano):\n";
    echo "  File .htm originali: $htm_count\n";
    echo "  File .php convertiti: $php_count\n";
    echo "  Progresso conversione: " . ($htm_count > 0 ? round(($php_count / $htm_count) * 100, 1) : 0) . "%\n\n";

    foreach ($lang_stats as $lang => $stats) {
        $lang_names = [
            'eng' => 'Inglese',
            'deu' => 'Tedesco',
            'spa' => 'Spagnolo',
            'fra' => 'Francese'
        ];

        echo "📁 FILE $lang (" . $lang_names[$lang] . "):\n";
        echo "  File .htm originali: " . $stats['htm'] . "\n";
        echo "  File .php convertiti: " . $stats['php'] . "\n";
        echo "  Progresso conversione: " . ($stats['htm'] > 0 ? round(($stats['php'] / $stats['htm']) * 100, 1) : 0) . "%\n\n";
    }

    // Verifica template system
    $template_files = [
        'includes/config.php',
        'includes/header.php',
        'includes/navigation.php',
        'includes/footer.php',
        'includes/init.php',
        'includes/migration-helper.php'
    ];

    echo "🔧 TEMPLATE SYSTEM:\n";
    foreach ($template_files as $file) {
        $status = file_exists($file) ? "✅" : "❌";
        echo "  $status $file\n";
    }

    echo "\n💡 RACCOMANDAZIONI:\n";
    if ($php_count == 0) {
        echo "  🚀 Inizia convertendo alcuni file con 'php tools/convert-single.php'\n";
    } elseif ($php_count < $htm_count) {
        echo "  📦 Completa la conversione con 'php tools/convert-batch.php'\n";
    } else {
        echo "  ✅ Conversione completata! Testa i file nel browser\n";
        echo "  🧹 Se necessario, pulisci i file con 'php tools/clean-converted-files.php'\n";
    }
}

/**
 * Mostra aiuto e documentazione
 */
function showHelp()
{
    echo "❓ AIUTO E DOCUMENTAZIONE\n";
    echo "========================\n\n";

    echo "📚 DOCUMENTAZIONE DISPONIBILE:\n";
    echo "  - TEMPLATE_SYSTEM_README.md (documentazione completa)\n";
    echo "  - tools/README.md (documentazione strumenti)\n\n";

    echo "🚀 COMANDI RAPIDI:\n";
    echo "  php tools/convert-single.php    # Converti un file\n";
    echo "  php tools/convert-batch.php     # Converti tutti i file\n";
    echo "  php tools/clean-converted-files.php # Pulisci file convertiti\n\n";

    echo "🔗 LINK UTILI:\n";
    echo "  - Visita index.php per vedere il template system in azione\n";
    echo "  - Visita demo.php per una dimostrazione completa\n\n";

    echo "⚠️  IMPORTANTE:\n";
    echo "  - Fai sempre un backup prima delle conversioni in batch\n";
    echo "  - Testa i file convertiti nel browser\n";
    echo "  - Usa 'php -l nomefile.php' per verificare la sintassi\n";
}
