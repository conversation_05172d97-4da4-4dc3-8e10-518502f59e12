<?php

/**
 * Script per conversione selettiva con controlli avanzati
 * Permette di scegliere quali file convertire
 *
 * Uso: php tools/convert-selective.php
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// Includi il migration helper
require_once 'includes/migration-helper.php';

// Funzione per mostrare anteprima della conversione
function previewConversion($html_file)
{
    if (!file_exists($html_file)) {
        echo "❌ File non trovato: $html_file\n";
        return false;
    }

    $html = file_get_contents($html_file);

    echo "\n📄 ANTEPRIMA CONVERSIONE: $html_file\n";
    echo str_repeat("=", 50) . "\n";

    // Estrai metadati
    $title = MigrationHelper::extractTitle($html);
    $description = MigrationHelper::extractDescription($html);
    $keywords = MigrationHelper::extractKeywords($html);

    echo "Titolo: " . ($title ?: "Non trovato") . "\n";
    echo "Descrizione: " . (strlen($description) > 100 ? substr($description, 0, 100) . "..." : $description) . "\n";
    echo "Keywords: " . (strlen($keywords) > 100 ? substr($keywords, 0, 100) . "..." : $keywords) . "\n";

    // Mostra prime righe del contenuto
    $content = MigrationHelper::extractMainContent($html);
    $content_preview = substr(strip_tags($content), 0, 200);
    echo "Contenuto (anteprima): " . $content_preview . "...\n";

    return true;
}

// Lista dei file da convertire con priorità
$priority_files = [
    'high' => [
        'presentazione.htm' => 'Pagina di presentazione principale',
        'contatti.htm' => 'Pagina contatti',
        'masseria.htm' => 'Descrizione della masseria'
    ],
    'medium' => [
        'camere.htm' => 'Descrizione camere',
        'ristorante.htm' => 'Informazioni ristorante',
        'servizi.htm' => 'Servizi offerti'
    ],
    'low' => [
        'storia.htm' => 'Storia della masseria',
        'posizione.htm' => 'Informazioni posizione',
        'dintorni.htm' => 'Attrazioni nei dintorni'
    ]
];

echo "🎯 CONVERSIONE SELETTIVA DEI FILE\n";
echo "=================================\n\n";

// Processa file ad alta priorità
echo "📌 FILE AD ALTA PRIORITÀ\n";
foreach ($priority_files['high'] as $file => $description) {
    echo "\n🔍 Analizzando: $file ($description)\n";

    if (previewConversion($file)) {
        echo "\n❓ Vuoi convertire questo file? (y/n): ";

        // Simula input utente (in un ambiente reale useresti readline o input da form)
        $convert = true; // Per demo, convertiamo automaticamente

        if ($convert) {
            try {
                $output = MigrationHelper::convertHtmlToTemplate($file);
                echo "✅ Convertito: $file → $output\n";

                // Verifica che il file convertito sia valido
                $syntax_check = shell_exec("php -l $output 2>&1");
                if (strpos($syntax_check, 'No syntax errors') !== false) {
                    echo "✅ Sintassi PHP valida\n";
                } else {
                    echo "⚠️  Attenzione: possibili errori di sintassi\n";
                    echo $syntax_check . "\n";
                }
            } catch (Exception $e) {
                echo "❌ Errore: " . $e->getMessage() . "\n";
            }
        } else {
            echo "⏭️  Saltato\n";
        }
    }
}

echo "\n📊 RIEPILOGO CONVERSIONE\n";
echo "========================\n";

// Conta file convertiti
$php_files = glob('*.php');
$htm_files = glob('*.htm');

echo "File PHP creati: " . count($php_files) . "\n";
echo "File HTM originali: " . count($htm_files) . "\n";

// Mostra file convertiti
echo "\n📁 File PHP disponibili:\n";
foreach ($php_files as $php_file) {
    if (
        $php_file !== 'demo.php' && $php_file !== 'convert-single.php' &&
        $php_file !== 'convert-batch.php' && $php_file !== 'convert-selective.php'
    ) {
        echo "  ✓ $php_file\n";
    }
}

echo "\n💡 PROSSIMI PASSI:\n";
echo "1. Testa i file convertiti nel browser\n";
echo "2. Verifica che tutti i link funzionino\n";
echo "3. Aggiorna i link interni per puntare ai file .php\n";
echo "4. Dopo il test, considera di rimuovere i file .htm originali\n";
