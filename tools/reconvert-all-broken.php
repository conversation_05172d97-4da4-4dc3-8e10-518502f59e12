<?php
/**
 * Script per riconvertire tutti i file che hanno problemi di sintassi
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON>ludi il migration helper
require_once 'includes/migration-helper.php';

echo "🔄 RICONVERSIONE FILE CON PROBLEMI\n";
echo "==================================\n\n";

// Lista dei file da riconvertire
$files_to_reconvert = [
    'camere.htm' => 'camere.php',
    'masseria.htm' => 'masseria.php', 
    'storia.htm' => 'storia.php'
];

foreach ($files_to_reconvert as $htm_file => $php_file) {
    echo "🔄 Riconvertendo: $htm_file -> $php_file\n";
    
    if (!file_exists($htm_file)) {
        echo "   ❌ File $htm_file non trovato!\n";
        continue;
    }
    
    try {
        $output_file = MigrationHelper::convertHtmlToTemplate($htm_file, $php_file);
        echo "   ✅ Conversione completata\n";
        
        // Verifica sintassi
        $syntax_check = shell_exec("php -l $output_file 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "   ✅ Sintassi PHP valida\n";
        } else {
            echo "   ❌ Errori di sintassi:\n";
            echo "   " . str_replace("\n", "\n   ", $syntax_check) . "\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Errore: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🎉 Riconversione completata!\n";
echo "💡 Tutti i file dovrebbero ora avere sintassi corretta.\n";
?>
