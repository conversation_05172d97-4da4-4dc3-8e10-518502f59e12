<?php
/**
 * Script per testare la conversione automatica dei percorsi degli asset
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON><PERSON>di il migration helper
require_once 'includes/migration-helper.php';

echo "🖼️  TEST CONVERSIONE PERCORSI ASSET\n";
echo "===================================\n\n";

/**
 * Analizza un file per trovare percorsi di asset
 */
function analyzeAssets($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $content = file_get_contents($file_path);
    
    // Cerca diversi tipi di asset
    $assets = [
        'images' => [],
        'css' => [],
        'js' => [],
        'relative_paths' => []
    ];
    
    // Pattern per trovare asset
    if (preg_match_all('#src=["\']([^"\']*?\.(jpg|jpeg|png|gif|svg|js))["\']#i', $content, $matches)) {
        foreach ($matches[1] as $i => $path) {
            $ext = strtolower($matches[2][$i]);
            if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'svg'])) {
                $assets['images'][] = $path;
            } elseif ($ext === 'js') {
                $assets['js'][] = $path;
            }
        }
    }
    
    if (preg_match_all('#href=["\']([^"\']*?\.css[^"\']*?)["\']#i', $content, $matches)) {
        $assets['css'] = array_merge($assets['css'], $matches[1]);
    }
    
    // Cerca percorsi relativi che iniziano con assets/
    if (preg_match_all('#(?:src|href)=["\']([^"\']*?assets/[^"\']*?)["\']#i', $content, $matches)) {
        $assets['relative_paths'] = array_unique($matches[1]);
    }
    
    return $assets;
}

// Testiamo con file in sottocartelle
$test_files = [
    'eng/presentazione.htm',
    'deu/contatti.htm',
    'spa/index.htm'
];

foreach ($test_files as $htm_file) {
    if (!file_exists($htm_file)) {
        echo "⚠️  File $htm_file non trovato, saltando...\n";
        continue;
    }
    
    echo "🔍 Analizzando: $htm_file\n";
    
    // Analizza asset nel file originale
    $original_assets = analyzeAssets($htm_file);
    
    if ($original_assets) {
        echo "   📊 Immagini trovate: " . count($original_assets['images']) . "\n";
        echo "   📊 CSS trovati: " . count($original_assets['css']) . "\n";
        echo "   📊 JS trovati: " . count($original_assets['js']) . "\n";
        echo "   📊 Percorsi relativi: " . count($original_assets['relative_paths']) . "\n";
        
        // Mostra alcuni esempi di percorsi relativi
        if (!empty($original_assets['relative_paths'])) {
            echo "   📄 Esempi percorsi da convertire:\n";
            foreach (array_slice($original_assets['relative_paths'], 0, 3) as $path) {
                echo "      - $path\n";
            }
        }
    }
    
    try {
        // Converti il file
        echo "   🔄 Convertendo file...\n";
        $php_file = str_replace('.htm', '.php', $htm_file);
        $output_file = MigrationHelper::convertHtmlToTemplate($htm_file, $php_file);
        
        // Analizza asset nel file convertito
        $converted_assets = analyzeAssets($output_file);
        
        // Cerca percorsi con ../ nel file convertito
        $content = file_get_contents($output_file);
        $back_paths = [];
        if (preg_match_all('#(?:src|href)=["\']([^"\']*?\.\./[^"\']*?)["\']#i', $content, $matches)) {
            $back_paths = array_unique($matches[1]);
        }
        
        echo "   ✅ Conversione completata\n";
        echo "   📊 Percorsi con ../ nel file convertito: " . count($back_paths) . "\n";
        
        if (!empty($back_paths)) {
            echo "   📄 Esempi percorsi convertiti:\n";
            foreach (array_slice($back_paths, 0, 3) as $path) {
                echo "      - $path\n";
            }
        }
        
        // Calcola tasso di conversione
        if (!empty($original_assets['relative_paths']) && !empty($back_paths)) {
            $conversion_rate = round((count($back_paths) / count($original_assets['relative_paths'])) * 100, 1);
            echo "   📈 Tasso di conversione percorsi: {$conversion_rate}%\n";
        }
        
        // Verifica sintassi
        $syntax_check = shell_exec("php -l $output_file 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "   ✅ Sintassi PHP valida\n";
        } else {
            echo "   ❌ Errori di sintassi\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Errore: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🎉 Test completato!\n";
echo "💡 Il migration helper ora converte automaticamente:\n";
echo "   🔗 Link interni da .htm/.html a .php\n";
echo "   🖼️  Percorsi asset per file in sottocartelle\n";
echo "   🍪 Rimuove banner policy cookie\n";
echo "   📁 Calcola percorsi include corretti\n";
?>
