<?php
/**
 * Script per testare la conversione automatica dei link interni
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON><PERSON><PERSON> il migration helper
require_once 'includes/migration-helper.php';

echo "🔗 TEST CONVERSIONE LINK INTERNI\n";
echo "=================================\n\n";

/**
 * Analizza un file per trovare link interni
 */
function analyzeLinks($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $content = file_get_contents($file_path);
    
    // Cerca link .htm e .html
    $htm_links = [];
    $html_links = [];
    
    // Pattern per trovare link
    if (preg_match_all('#href=["\']([^"\']*?\.htm)["\']#i', $content, $matches)) {
        $htm_links = array_unique($matches[1]);
    }
    
    if (preg_match_all('#href=["\']([^"\']*?\.html)["\']#i', $content, $matches)) {
        $html_links = array_unique($matches[1]);
    }
    
    return [
        'htm_links' => $htm_links,
        'html_links' => $html_links,
        'total_links' => count($htm_links) + count($html_links)
    ];
}

// Testiamo con un file che ha molti link
$test_files = [
    'natura.htm',
    'gallery.htm',
    'offerte.htm'
];

foreach ($test_files as $htm_file) {
    if (!file_exists($htm_file)) {
        echo "⚠️  File $htm_file non trovato, saltando...\n";
        continue;
    }
    
    echo "🔍 Analizzando: $htm_file\n";
    
    // Analizza link nel file originale
    $original_links = analyzeLinks($htm_file);
    
    if ($original_links['total_links'] > 0) {
        echo "   📊 Link .htm trovati: " . count($original_links['htm_links']) . "\n";
        echo "   📊 Link .html trovati: " . count($original_links['html_links']) . "\n";
        
        // Mostra alcuni esempi
        if (!empty($original_links['htm_links'])) {
            echo "   📄 Esempi link .htm:\n";
            foreach (array_slice($original_links['htm_links'], 0, 3) as $link) {
                echo "      - $link\n";
            }
        }
        
        if (!empty($original_links['html_links'])) {
            echo "   📄 Esempi link .html:\n";
            foreach (array_slice($original_links['html_links'], 0, 3) as $link) {
                echo "      - $link\n";
            }
        }
    } else {
        echo "   ℹ️  Nessun link interno .htm/.html trovato\n";
    }
    
    try {
        // Converti il file
        echo "   🔄 Convertendo file...\n";
        $php_file = str_replace('.htm', '.php', $htm_file);
        $output_file = MigrationHelper::convertHtmlToTemplate($htm_file, $php_file);
        
        // Analizza link nel file convertito
        $converted_links = analyzeLinks($output_file);
        
        // Cerca link .php nel file convertito
        $content = file_get_contents($output_file);
        $php_links = [];
        if (preg_match_all('#href=["\']([^"\']*?\.php)["\']#i', $content, $matches)) {
            $php_links = array_unique($matches[1]);
        }
        
        echo "   ✅ Conversione completata\n";
        echo "   📊 Link .php nel file convertito: " . count($php_links) . "\n";
        echo "   📊 Link .htm rimasti: " . count($converted_links['htm_links']) . "\n";
        echo "   📊 Link .html rimasti: " . count($converted_links['html_links']) . "\n";
        
        if (!empty($php_links)) {
            echo "   📄 Esempi link convertiti:\n";
            foreach (array_slice($php_links, 0, 3) as $link) {
                echo "      - $link\n";
            }
        }
        
        if ($original_links['total_links'] > 0 && count($php_links) > 0) {
            $conversion_rate = round((count($php_links) / $original_links['total_links']) * 100, 1);
            echo "   📈 Tasso di conversione: {$conversion_rate}%\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ Errore: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🎉 Test completato!\n";
echo "💡 Il migration helper ora converte automaticamente i link interni da .htm/.html a .php\n";
?>
