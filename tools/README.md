# 🛠️ Strumenti di Migrazione Template System

Questa cartella contiene tutti gli script di utilità per la migrazione dal vecchio sistema HTML statico al nuovo sistema di template PHP.

## 📁 Contenuto della Cartella

### Script di Conversione

- **`convert-single.php`** - Converte un singolo file HTML
- **`convert-batch.php`** - Conversione in batch di tutti i file .htm
- **`convert-subdirectories.php`** - Conversione file nelle sottocartelle delle lingue
- **`convert-selective.php`** - Conversione selettiva con controlli avanzati

### Script di Manutenzione

- **`clean-converted-files.php`** - Pulisce i file PHP già convertiti da tag inutili (avanzato)
- **`clean-files-simple.php`** - Pulizia semplice e veloce dei file convertiti
- **`fix-converted-files.php`** - Correzione avanzata dei file con problemi
- **`fix-asset-paths.php`** - Corregge percorsi asset nelle sottocartelle delle lingue
- **`remove-cookie-banner.php`** - Rimuove banner policy cookie dai file convertiti
- **`reconvert-presentazione.php`** - Riconverte presentazione.htm con migration helper migliorato

### Script di Dimostrazione

- **`demo.php`** - Dimostra i benefici del nuovo sistema di template

### Script di Utilità

- **`run.php`** - Esegue strumenti specifici da riga di comando
- **`menu.php`** - Menu interattivo semplificato
- **`convert.php`** - Convertitore semplice da riga di comando per file singoli

## 🚀 Come Usare gli Strumenti

### Metodo 1: Comandi Diretti

### Conversione Singola

```bash
# Conversione con file specificato (RACCOMANDATO)
php tools/convert.php contatti.htm
php tools/convert.php contatti        # estensione .htm aggiunta automaticamente

# Conversione con file predefiniti
php tools/convert-single.php
```

### Conversione in Batch

```bash
# Converte tutti i file .htm di tutte le lingue
php tools/convert-batch.php

# Converte file nelle sottocartelle delle lingue
php tools/convert-subdirectories.php        # Tutte le lingue
php tools/convert-subdirectories.php eng    # Solo inglese
php tools/convert-subdirectories.php deu    # Solo tedesco
```

### Conversione Selettiva

```bash
# Permette di scegliere quali file convertire
php tools/convert-selective.php
```

### Pulizia File Convertiti

```bash
# Rimuove tag HTML inutili dai file già convertiti
php tools/clean-converted-files.php

# Pulizia semplice e veloce
php tools/clean-files-simple.php

# Rimuove banner policy cookie
php tools/remove-cookie-banner.php
```

### Riconversione Specifica

```bash
# Riconverte presentazione.htm con migration helper migliorato
php tools/reconvert-presentazione.php
```

### Demo del Sistema

```bash
# Genera demo.php nella root, poi visitalo nel browser
php tools/demo.php
```

### Metodo 2: Script di Utilità

```bash
# Esegui strumento specifico da riga di comando
php tools/run.php 7    # Mostra stato progetto
php tools/run.php 1    # Conversione singola
php tools/run.php 2    # Conversione batch

# Menu interattivo semplificato
php tools/menu.php
```

## ⚠️ Note Importanti

1. **Directory di Lavoro**: Tutti gli script cambiano automaticamente la directory di lavoro alla root del sito
2. **Backup**: Fai sempre un backup prima di eseguire conversioni in batch
3. **Test**: Testa sempre i file convertiti nel browser prima di rimuovere gli originali
4. **Percorsi**: Gli script funzionano dalla cartella `tools/` ma operano sui file nella root

## 📋 Ordine di Utilizzo Consigliato

1. **Test iniziale**: `php tools/convert-single.php` (per testare un file)
2. **Demo**: `php tools/demo.php` (per vedere i benefici)
3. **Conversione completa**: `php tools/convert-batch.php`
4. **Pulizia**: `php tools/clean-converted-files.php` (se necessario)
5. **Test finale**: Verifica tutti i file convertiti nel browser

## 🔧 Personalizzazione

Per modificare quali file convertire, edita gli array nei file di conversione:

```php
$files_to_convert = [
    'contatti.htm',
    'masseria.htm',
    'storia.htm',
    // aggiungi altri file...
];
```

## 📊 Output degli Script

Gli script forniscono output dettagliato con:

- ✅ Operazioni completate con successo
- ❌ Errori riscontrati
- ⚠️ Avvertimenti e note
- 📊 Statistiche di conversione
- 💡 Suggerimenti per i prossimi passi

## 🆘 Risoluzione Problemi

### Errore "File non trovato"

- Verifica che il file .htm esista nella directory corretta
- Controlla i permessi di lettura del file

### Errore di sintassi PHP

- Usa `php -l nomefile.php` per verificare la sintassi
- Controlla che il contenuto HTML sia ben formato

### Tag di chiusura inutili

- Usa `clean-converted-files.php` per pulire automaticamente
- Il migration helper migliorato previene questo problema

## 🔄 Aggiornamenti

Quando aggiorni il migration helper in `includes/migration-helper.php`, tutti gli script in questa cartella utilizzeranno automaticamente la versione aggiornata.
