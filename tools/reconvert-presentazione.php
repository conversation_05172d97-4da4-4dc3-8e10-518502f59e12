<?php

/**
 * Script per riconvertire presentazione.htm con il migration helper migliorato
 *
 * Uso: php tools/reconvert-presentazione.php
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// Includi il migration helper aggiornato
require_once 'includes/migration-helper.php';

echo "🔄 RICONVERSIONE DI presentazione.htm\n";
echo "=====================================\n\n";

// Verifica che il file originale esista
if (!file_exists('presentazione.htm')) {
    echo "❌ File presentazione.htm non trovato!\n";
    echo "💡 Assicurati che il file sia presente nella directory principale.\n";
    exit;
}

// Backup del file PHP esistente se presente
if (file_exists('presentazione.php')) {
    $backup_name = 'presentazione_backup_' . date('Y-m-d_H-i-s') . '.php';
    copy('presentazione.php', $backup_name);
    echo "📦 Backup creato: $backup_name\n";
}

echo "🔍 Analizzando presentazione.htm...\n";

// Leggi il file HTML originale
$html_content = file_get_contents('presentazione.htm');

// Estrai metadati
$title = MigrationHelper::extractTitle($html_content);
$description = MigrationHelper::extractDescription($html_content);
$keywords = MigrationHelper::extractKeywords($html_content);

echo "📄 Metadati estratti:\n";
echo "  Titolo: " . ($title ?: "Non trovato") . "\n";
echo "  Descrizione: " . (strlen($description) > 100 ? substr($description, 0, 100) . "..." : $description) . "\n";
echo "  Keywords: " . (strlen($keywords) > 100 ? substr($keywords, 0, 100) . "..." : $keywords) . "\n\n";

// Estrai contenuto con il migration helper migliorato
echo "🔧 Estraendo contenuto con migration helper migliorato...\n";
$content = MigrationHelper::extractMainContent($html_content);

// Mostra anteprima del contenuto estratto
echo "📋 Anteprima contenuto estratto (prime 10 righe):\n";
$content_lines = explode("\n", $content);
for ($i = 0; $i < min(10, count($content_lines)); $i++) {
    $line = trim($content_lines[$i]);
    if (!empty($line)) {
        echo "  " . ($i + 1) . ": " . (strlen($line) > 80 ? substr($line, 0, 80) . "..." : $line) . "\n";
    }
}

echo "\n🚀 Generando nuovo file presentazione.php...\n";

try {
    // Usa il migration helper per convertire
    $output_file = MigrationHelper::convertHtmlToTemplate('presentazione.htm', 'presentazione.php');

    echo "✅ Conversione completata: $output_file\n";

    // Verifica sintassi PHP
    $syntax_check = shell_exec("php -l $output_file 2>&1");
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "✅ Sintassi PHP valida\n";
    } else {
        echo "⚠️  Attenzione: possibili errori di sintassi:\n";
        echo $syntax_check . "\n";
    }

    // Mostra statistiche
    $original_size = filesize('presentazione.htm');
    $new_size = filesize('presentazione.php');

    echo "\n📊 STATISTICHE:\n";
    echo "  File originale: " . number_format($original_size) . " bytes\n";
    echo "  File convertito: " . number_format($new_size) . " bytes\n";
    echo "  Differenza: " . ($new_size > $original_size ? "+" : "") . number_format($new_size - $original_size) . " bytes\n";

    // Conta righe di codice
    $original_lines = count(file('presentazione.htm'));
    $new_lines = count(file('presentazione.php'));

    echo "  Righe originali: $original_lines\n";
    echo "  Righe convertite: $new_lines\n";
    echo "  Riduzione: " . round((($original_lines - $new_lines) / $original_lines) * 100, 1) . "%\n";

    echo "\n💡 PROSSIMI PASSI:\n";
    echo "1. Apri presentazione.php nel browser per testarlo\n";
    echo "2. Verifica che tutti i contenuti siano visualizzati correttamente\n";
    echo "3. Controlla che i link e le immagini funzionino\n";
    echo "4. Se tutto è OK, puoi procedere con altri file\n";
} catch (Exception $e) {
    echo "❌ Errore durante la conversione: " . $e->getMessage() . "\n";
}

echo "\n🎯 CONFRONTO PRIMA/DOPO:\n";
echo "========================\n";
echo "PRIMA (HTML statico):\n";
echo "- File duplicato per ogni lingua\n";
echo "- Header/footer ripetuti in ogni file\n";
echo "- Manutenzione complessa\n\n";

echo "DOPO (Template PHP):\n";
echo "- Un solo file per tutte le lingue\n";
echo "- Header/footer centralizzati\n";
echo "- Manutenzione semplificata\n";
echo "- Contenuto pulito senza tag inutili\n";
