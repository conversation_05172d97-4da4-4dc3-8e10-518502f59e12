<?php
/**
 * Script semplice per pulire i file PHP convertiti
 * Rimuove tag di chiusura e commenti HTML all'inizio del contenuto
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

echo "🧹 PULIZIA SEMPLICE FILE PHP\n";
echo "============================\n\n";

// Lista dei file da pulire
$files_to_clean = [
    'contatti.php',
    'camere.php', 
    'masseria.php',
    'storia.php'
];

foreach ($files_to_clean as $file) {
    if (!file_exists($file)) {
        echo "⚠️  File non trovato: $file\n";
        continue;
    }
    
    echo "🔧 Pulendo: $file\n";
    
    // Leggi il file
    $content = file_get_contents($file);
    
    // Trova la sezione del contenuto
    if (preg_match('/(.*?function renderContent\(\) \{[^}]*\?>)(.*?)(<\?php\s*\}.*)/s', $content, $matches)) {
        $before = $matches[1];
        $html_content = $matches[2];
        $after = $matches[3];
        
        // Pulisci il contenuto HTML
        $lines = explode("\n", $html_content);
        $cleaned_lines = [];
        $content_started = false;
        
        foreach ($lines as $line) {
            $trimmed = trim($line);
            
            // Se il contenuto non è ancora iniziato
            if (!$content_started) {
                // Salta righe vuote
                if (empty($trimmed)) {
                    continue;
                }
                
                // Salta commenti HTML come <!-- Header -->
                if (preg_match('/^<!--.*-->$/', $trimmed)) {
                    continue;
                }
                
                // Salta tag di chiusura come </div>
                if (preg_match('/^<\/\w+>$/', $trimmed)) {
                    continue;
                }
                
                // Se arriviamo qui, il contenuto è iniziato
                $content_started = true;
            }
            
            $cleaned_lines[] = $line;
        }
        
        // Rimuovi righe vuote eccessive all'inizio
        while (!empty($cleaned_lines) && trim($cleaned_lines[0]) === '') {
            array_shift($cleaned_lines);
        }
        
        // Ricostruisci il file
        $cleaned_html = implode("\n", $cleaned_lines);
        $new_content = $before . $cleaned_html . $after;
        
        // Salva il file
        file_put_contents($file, $new_content);
        
        echo "   ✅ Pulito con successo\n";
        
        // Mostra le prime righe del contenuto pulito
        $preview_lines = array_slice($cleaned_lines, 0, 3);
        foreach ($preview_lines as $i => $line) {
            $preview = strlen(trim($line)) > 50 ? substr(trim($line), 0, 50) . "..." : trim($line);
            if (!empty($preview)) {
                echo "   " . ($i + 1) . ": $preview\n";
                break;
            }
        }
        
    } else {
        echo "   ❌ Struttura non riconosciuta\n";
    }
    
    echo "\n";
}

echo "🎉 Pulizia completata!\n";
echo "💡 Controlla i file nel browser per verificare che funzionino correttamente.\n";
?>
