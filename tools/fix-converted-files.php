<?php
/**
 * Script per correggere i file PHP già convertiti che hanno tag inutili
 * Rimuove tag di chiusura, commenti HTML e righe vuote all'inizio del contenuto
 * 
 * Uso: php tools/fix-converted-files.php
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// Includi il migration helper aggiornato
require_once 'includes/migration-helper.php';

/**
 * Pulisce il contenuto di un file PHP già convertito
 */
function fixConvertedPhpFile($file_path) {
    if (!file_exists($file_path)) {
        echo "❌ File non trovato: $file_path\n";
        return false;
    }
    
    $content = file_get_contents($file_path);
    
    // Pattern per trovare la funzione renderContent
    $pattern = '/(function renderContent\(\) \{[^}]*\?>)(.*?)(<\?php\s*\})/s';
    
    if (preg_match($pattern, $content, $matches)) {
        $before = $matches[1];
        $html_content = $matches[2];
        $after = $matches[3];
        
        echo "🔍 Analizzando: $file_path\n";
        
        // Mostra il contenuto problematico se presente
        $lines = explode("\n", $html_content);
        $has_issues = false;
        
        // Controlla le prime 10 righe per problemi
        for ($i = 0; $i < min(10, count($lines)); $i++) {
            $trimmed = trim($lines[$i]);
            if (preg_match('#^</div>$#', $trimmed) || 
                preg_match('#^<!--.*-->$#', $trimmed) ||
                (empty($trimmed) && $i < 5)) {
                $has_issues = true;
                break;
            }
        }
        
        if ($has_issues) {
            echo "⚠️  Trovati tag/commenti inutili all'inizio\n";
            
            // Pulisci il contenuto HTML usando la funzione migliorata
            $cleaned_html = cleanHtmlContentAdvanced($html_content);
            
            // Ricostruisci il file
            $new_content = $before . $cleaned_html . $after;
            
            // Salva il file pulito
            file_put_contents($file_path, $new_content);
            
            echo "✅ File pulito: $file_path\n";
            return true;
        } else {
            echo "✅ File già pulito: $file_path\n";
            return true;
        }
    } else {
        echo "⚠️  Struttura non riconosciuta in: $file_path\n";
        return false;
    }
}

/**
 * Pulisce il contenuto HTML estratto con algoritmo avanzato
 */
function cleanHtmlContentAdvanced($html_content) {
    // Dividi in righe per analisi riga per riga
    $lines = explode("\n", $html_content);
    $cleaned_lines = [];
    $content_started = false;
    
    foreach ($lines as $line) {
        $trimmed = trim($line);
        
        // Se il contenuto non è ancora iniziato, salta righe problematiche
        if (!$content_started) {
            // Salta righe vuote
            if (empty($trimmed)) {
                continue;
            }
            
            // Salta commenti HTML
            if (preg_match('#^<!--.*-->$#', $trimmed)) {
                continue;
            }
            
            // Salta tag di chiusura isolati
            if (preg_match('#^</\w+>$#', $trimmed)) {
                continue;
            }
            
            // Salta righe con solo spazi
            if (preg_match('#^\s+$#', $line)) {
                continue;
            }
            
            // Se arriviamo qui, il contenuto è iniziato
            $content_started = true;
        }
        
        // Aggiungi la riga
        $cleaned_lines[] = $line;
    }
    
    // Rimuovi righe vuote eccessive all'inizio
    while (!empty($cleaned_lines) && trim($cleaned_lines[0]) === '') {
        array_shift($cleaned_lines);
    }
    
    return implode("\n", $cleaned_lines);
}

/**
 * Mostra anteprima delle modifiche senza applicarle
 */
function previewFix($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $content = file_get_contents($file_path);
    $pattern = '/(function renderContent\(\) \{[^}]*\?>)(.*?)(<\?php\s*\})/s';
    
    if (preg_match($pattern, $content, $matches)) {
        $html_content = $matches[2];
        
        echo "\n📄 ANTEPRIMA: $file_path\n";
        echo str_repeat("=", 50) . "\n";
        
        // Mostra prime righe del contenuto attuale
        $lines = explode("\n", $html_content);
        echo "CONTENUTO ATTUALE (prime 8 righe):\n";
        for ($i = 0; $i < min(8, count($lines)); $i++) {
            $line_preview = strlen($lines[$i]) > 60 ? substr($lines[$i], 0, 60) . "..." : $lines[$i];
            echo sprintf("%2d: %s\n", $i + 1, $line_preview);
        }
        
        // Mostra contenuto dopo pulizia
        $cleaned = cleanHtmlContentAdvanced($html_content);
        $cleaned_lines = explode("\n", $cleaned);
        echo "\nCONTENUTO DOPO PULIZIA (prime 8 righe):\n";
        for ($i = 0; $i < min(8, count($cleaned_lines)); $i++) {
            $line_preview = strlen($cleaned_lines[$i]) > 60 ? substr($cleaned_lines[$i], 0, 60) . "..." : $cleaned_lines[$i];
            echo sprintf("%2d: %s\n", $i + 1, $line_preview);
        }
        
        return true;
    }
    
    return false;
}

// Script principale
echo "🔧 CORREZIONE FILE PHP CONVERTITI\n";
echo "==================================\n\n";

// Trova tutti i file PHP (esclusi gli script di utilità)
$php_files = glob('*.php');
$utility_files = ['demo.php'];

$files_to_fix = array_diff($php_files, $utility_files);

if (empty($files_to_fix)) {
    echo "ℹ️  Nessun file PHP da correggere trovato.\n";
    exit;
}

echo "📁 File trovati da analizzare:\n";
foreach ($files_to_fix as $file) {
    echo "  - $file\n";
}

echo "\n🔍 ANTEPRIMA DELLE CORREZIONI\n";
echo "=============================\n";

// Mostra anteprima per ogni file
foreach ($files_to_fix as $file) {
    previewFix($file);
}

echo "\n🚀 APPLICAZIONE DELLE CORREZIONI\n";
echo "=================================\n";

$fixed_count = 0;
$error_count = 0;

foreach ($files_to_fix as $file) {
    if (fixConvertedPhpFile($file)) {
        $fixed_count++;
    } else {
        $error_count++;
    }
}

echo "\n📊 RIEPILOGO\n";
echo "============\n";
echo "File processati: " . count($files_to_fix) . "\n";
echo "File corretti: $fixed_count\n";
echo "Errori: $error_count\n";

if ($fixed_count > 0) {
    echo "\n✅ Correzione completata! I file sono ora puliti.\n";
    echo "💡 Ricorda di testare i file nel browser per verificare che tutto funzioni correttamente.\n";
}
?>
