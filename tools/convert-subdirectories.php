<?php
/**
 * Script per convertire file HTML nelle sottocartelle delle lingue
 * 
 * Uso: php tools/convert-subdirectories.php [lingua]
 * Esempio: php tools/convert-subdirectories.php eng
 * Oppure: php tools/convert-subdirectories.php (converte tutte le lingue)
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// Includi il migration helper
require_once 'includes/migration-helper.php';

echo "🌍 CONVERSIONE FILE SOTTOCARTELLE LINGUE\n";
echo "========================================\n\n";

// Definisci le cartelle delle lingue
$language_dirs = ['eng', 'deu', 'spa', 'fra'];

// Controlla se è stata specificata una lingua
$target_lang = isset($argv[1]) ? $argv[1] : null;

if ($target_lang) {
    if (in_array($target_lang, $language_dirs)) {
        $language_dirs = [$target_lang];
        echo "🎯 Convertendo solo la lingua: $target_lang\n\n";
    } else {
        echo "❌ Lingua non riconosciuta: $target_lang\n";
        echo "💡 Lingue disponibili: " . implode(', ', $language_dirs) . "\n";
        exit(1);
    }
} else {
    echo "🌐 Convertendo tutte le lingue...\n\n";
}

$total_converted = 0;
$total_errors = 0;

foreach ($language_dirs as $lang_dir) {
    if (!is_dir($lang_dir)) {
        echo "⚠️  Cartella $lang_dir non trovata, saltando...\n\n";
        continue;
    }
    
    echo "📁 Processando cartella: $lang_dir\n";
    echo str_repeat("-", 30) . "\n";
    
    // Trova tutti i file .htm nella cartella
    $htm_files = glob("$lang_dir/*.htm");
    
    if (empty($htm_files)) {
        echo "ℹ️  Nessun file .htm trovato in $lang_dir\n\n";
        continue;
    }
    
    echo "📊 File .htm trovati: " . count($htm_files) . "\n";
    
    $converted_count = 0;
    $error_count = 0;
    
    foreach ($htm_files as $htm_file) {
        $filename = basename($htm_file);
        echo "   🔄 Convertendo: $filename";
        
        try {
            $php_file = str_replace('.htm', '.php', $htm_file);
            $output_file = MigrationHelper::convertHtmlToTemplate($htm_file, $php_file);
            
            // Verifica sintassi
            $syntax_check = shell_exec("php -l $output_file 2>&1");
            if (strpos($syntax_check, 'No syntax errors') !== false) {
                echo " ✅\n";
                $converted_count++;
            } else {
                echo " ❌ (errori sintassi)\n";
                $error_count++;
            }
            
        } catch (Exception $e) {
            echo " ❌ (" . $e->getMessage() . ")\n";
            $error_count++;
        }
    }
    
    echo "\n📊 Risultati per $lang_dir:\n";
    echo "   ✅ Convertiti: $converted_count\n";
    echo "   ❌ Errori: $error_count\n";
    echo "   📈 Successo: " . ($converted_count > 0 ? round(($converted_count / count($htm_files)) * 100, 1) : 0) . "%\n\n";
    
    $total_converted += $converted_count;
    $total_errors += $error_count;
}

echo "🎉 RIEPILOGO FINALE\n";
echo "===================\n";
echo "File convertiti totali: $total_converted\n";
echo "Errori totali: $total_errors\n";

if ($total_converted > 0) {
    echo "\n✅ Conversione completata!\n";
    echo "💡 I file nelle sottocartelle ora usano il percorso corretto '../includes/init.php'\n";
    echo "🔗 Tutti i link interni sono stati convertiti da .htm a .php\n";
    echo "🍪 I banner delle policy cookie sono stati rimossi automaticamente\n";
} else {
    echo "\n⚠️  Nessun file convertito.\n";
    echo "💡 Controlla che le cartelle delle lingue contengano file .htm\n";
}
?>
