<?php
/**
 * Script per riconvertire contatti.htm correttamente
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON>ludi il migration helper
require_once 'includes/migration-helper.php';

echo "🔄 Riconversione di contatti.htm\n";
echo "=================================\n\n";

if (!file_exists('contatti.htm')) {
    echo "❌ File contatti.htm non trovato!\n";
    exit;
}

try {
    $output_file = MigrationHelper::convertHtmlToTemplate('contatti.htm', 'contatti.php');
    echo "✅ Conversione completata: $output_file\n";
    
    // Verifica sintassi
    $syntax_check = shell_exec("php -l $output_file 2>&1");
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "✅ Sintassi PHP valida\n";
    } else {
        echo "❌ Errori di sintassi:\n";
        echo $syntax_check . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Errore: " . $e->getMessage() . "\n";
}
?>
