# Masseria Torricella Template System

## Overview

This PHP-based template system dramatically reduces code duplication and improves maintainability for the multilingual Masseria Torricella website. Instead of maintaining 5+ copies of each page, you now have a centralized system that manages layout, navigation, and content.

## Benefits

- **90% reduction** in duplicated HTML code
- **Single point of maintenance** for layout changes
- **Consistent navigation** across all languages
- **Easy content updates** without touching HTML structure
- **Automatic language detection** and asset path management
- **SEO-friendly** with proper meta tags and structure

## File Structure

```
includes/
├── config.php          # Central configuration and language settings
├── header.php          # HTML head section and opening body tag
├── navigation.php      # Main navigation menu (multilingual)
├── footer.php          # Footer content and closing scripts
├── init.php            # Template system initialization
└── migration-helper.php # Helper for converting existing HTML files

tools/
├── convert-single.php  # Convert single HTML file
├── convert-batch.php   # Batch convert all .htm files
├── convert-selective.php # Selective conversion with advanced controls
├── clean-converted-files.php # Clean already converted PHP files
├── reconvert-presentazione.php # Reconvert specific files
├── demo.php           # Template system demonstration
└── README.md          # Tools documentation

index.php               # Example homepage using template system
presentazione.php       # Example content page using template system
```

## Quick Start

### 1. Creating a New Page

```php
<?php
// Initialize template system
require_once 'includes/init.php';

// Page configuration
$page_config = [
    'page_title' => 'Your Page Title',
    'page_description' => 'Your page description for SEO',
    'page_keywords' => 'your, keywords, here',
    'current_page' => 'yourpage.htm'  // For navigation highlighting
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

<!-- Your page content here -->
<div class="container">
    <h1>Your Page Content</h1>
    <p>This is your page content...</p>
</div>

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>
```

### 2. Converting Existing HTML Files

Use the migration tools to convert existing HTML files:

```bash
# Convert a single file
php tools/convert-single.php

# Batch convert all .htm files
php tools/convert-batch.php

# Selective conversion with preview
php tools/convert-selective.php

# Clean already converted files
php tools/clean-converted-files.php
```

## Configuration

### Adding New Languages

Edit `includes/config.php` and add to the `$languages` array:

```php
'pt' => [
    'name' => 'Português',
    'code' => 'pt',
    'flag' => 'por',
    'dir' => 'por/',
    'home' => 'por/index.html'
]
```

### Adding Navigation Items

Edit the `$navigation` array in `includes/config.php`:

```php
'new_section' => [
    'title' => 'New Section',
    'dropdown' => [
        'item1' => ['url' => 'page1.htm', 'title' => 'Page 1'],
        'item2' => ['url' => 'page2.htm', 'title' => 'Page 2']
    ]
]
```

### Site Configuration

Update `$site_config` in `includes/config.php` for contact info, analytics, etc.

## Helper Functions

### getText($key, $lang = null)

Get localized text strings:

```php
echo getText('welcome'); // Returns "Benvenuti su" for Italian
echo getText('read_more'); // Returns "Leggi" for Italian
```

### getCurrentLanguage()

Detect current language from URL:

```php
$lang = getCurrentLanguage(); // Returns 'it', 'en', 'de', etc.
```

### getAssetPath($lang)

Get correct asset path for current language:

```php
$assets = getAssetPath($lang); // Returns 'assets/' or 'eng/assets/' etc.
```

## Migration Strategy

### Phase 1: Core Pages (Recommended)

1. Convert main pages: index, presentazione, contatti
2. Test functionality and styling
3. Update internal links to point to .php files

### Phase 2: Content Pages

1. Convert remaining content pages
2. Update navigation links
3. Test all language versions

### Phase 3: Cleanup

1. Remove old .htm files (after backup)
2. Update .htaccess for URL redirects if needed
3. Update sitemap.xml

## Best Practices

### 1. Content Organization

- Keep content in the `renderContent()` function
- Use helper functions for repetitive elements
- Separate data from presentation

### 2. Asset Management

- Always use `$asset_path` for images, CSS, JS
- Test all language versions after changes
- Maintain consistent file structure

### 3. SEO Considerations

- Set proper page titles and descriptions
- Use semantic HTML structure
- Maintain existing URL structure when possible

## Troubleshooting

### Common Issues

1. **Assets not loading**: Check `getAssetPath()` usage
2. **Navigation not highlighting**: Verify `current_page` setting
3. **Language detection failing**: Check URL structure and language config

### Debug Mode

Add this to any page for debugging:

```php
echo '<pre>';
echo 'Current Language: ' . getCurrentLanguage() . "\n";
echo 'Asset Path: ' . getAssetPath() . "\n";
echo 'Current Page: ' . (isset($current_page) ? $current_page : 'not set') . "\n";
echo '</pre>';
```

## Performance Notes

- Template system adds minimal overhead
- Consider enabling PHP OPcache for production
- Static assets remain unchanged and cacheable
- Database integration can be added later if needed

## Future Enhancements

- Content management system integration
- Dynamic content loading
- Advanced caching mechanisms
- Multi-domain language support
- Admin interface for content editing

## Support

For questions or issues with the template system, refer to this documentation or contact the development team.
