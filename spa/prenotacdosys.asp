<!--METADATA TYPE="typelib" UUID="CD000000-8B95-11D1-82DB-00C04FB1625D" NAME="CDO for Windows 2000 Type Library" -->
<!--METADATA TYPE="typelib" UUID="00000205-0000-0010-8000-00AA006D2EA4" NAME="ADODB Type Library" -->
<%
DIM corpoMessaggio, numeroCampi, invioA, invioDa, nomeDominio, indirizzoIp, modulo, browserSistemaOperativo

	'* voce da modificare con il proprio indirizzo email
	
invioA =  "<EMAIL>"
invioBcc =  "<EMAIL>"

	'* voce da modificare con un indirizzo email che funga da mittente: 
	'* in caso di errore riceverete notifica a questo indirizzo un MAILER-DAEMON
	'* dato che cdosys supporta questa notifica
		
invioDa =  "<EMAIL>"

'------------fine modifiche necessarie------------------

nomeDominio 				= Request.ServerVariables("HTTP_HOST")
indirizzoIp					= Request.ServerVariables("REMOTE_ADDR") 
modulo						= Request.ServerVariables("HTTP_REFERER")
browserSistemaOperativo		= Request.ServerVariables("HTTP_USER_AGENT")

	'*rilevo i campi del form
	
FOR numeroCampi = 1 TO (Request.Form.Count() - 1)
   IF NOT Request.Form(numeroCampi) = "" THEN
      corpoMessaggio = corpoMessaggio & vbCrLf & Request.Form.Key(numeroCampi) & " = " & Trim(Request.Form(numeroCampi))
   END IF
NEXT

	'* creo gli oggetti cdosys sul server e li gestisco
	
DIM iMsg, Flds, iConf

Set iMsg = CreateObject("CDO.Message")
Set iConf = CreateObject("CDO.Configuration")
Set Flds = iConf.Fields

Flds(cdoSendUsingMethod) = cdoSendUsingPort
Flds(cdoSMTPServer) = "localhost" 
Flds(cdoSMTPServerPort) = 25
Flds(cdoSMTPAuthenticate) = cdoAnonymous ' 0
Flds.Update

With iMsg
   Set .Configuration = iConf
   .To = invioA
   .Bcc = invioBcc
   .From = Request.Form("email")
   .Subject = "Modulo di Prenotazione dal dominio " & nomeDominio
   .TextBody = "Questi i dati inseriti nel modulo presente alla pagina di prenotazione" & vbCrLf & corpoMessaggio & ""
   .Send
End With
%> 
<script language="JavaScript">

document.write("<body bgcolor=ffffff><!-- inizio banner policy cookie  -->
<link href="http://www.acti-web.com/privacy/stili.css" rel="stylesheet" type="text/css">
<iframe id='cookie_iframe' src="http://www.acti-web.com/privacy/privacy_global.php?s=masseriatorricella"></iframe>
<!-- fine banner policy cookie -->
    </body>");
alert("Invio effettuato correttamente");
if (window.opener){
window.close();
} 
else
{ 
history.go(-1);
}
</script>
