<HTML>
<HEAD>
 <TITLE>Zoom</TITLE>
 <script language='javascript'>
   var arrTemp=self.location.href.split("?");
   var picUrl = (arrTemp.length>0)?arrTemp[1]:"";
  var NS = (navigator.AppName=="Netscape")?true:false;
   

      function FitPic() {
	  if (navigator.userAgent.indexOf("Firefox")!=-1) {
       iWidth = (NS)?window.innerWidth:document.body.clientWidth;
	   iHeight = (NS)?window.innerHeight:document.body.clientHeight;
       iWidth = document.images[0].width ;
       iHeight = document.images[0].height;
       window.resizeTo(iWidth+7, iHeight+53);
	   alert();
	   } else {
		   iWidth = (NS)?window.innerWidth:document.body.clientWidth;
		   iHeight = (NS)?window.innerHeight:document.body.clientHeight;
		   iWidth = document.images[0].width - iWidth;
		   iHeight = document.images[0].height - iHeight;
		   window.resizeBy(iWidth, iHeight);
	   }
       self.focus();
     };
 </script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-XQ3WQSR0K6"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-XQ3WQSR0K6');
</script>
</head>
<BODY bgcolor="#000000" onload='FitPic();' topmargin="0"  
marginheight="0" leftmargin="0" marginwidth="0">
 <script language='javascript'>
 document.write( "<img src='" + picUrl + "' border=0>" );
 </script>
<!-- inizio banner policy cookie  -->
<link href="http://www.acti-web.com/privacy/stili.css" rel="stylesheet" type="text/css">
<iframe id='cookie_iframe' src="http://www.acti-web.com/privacy/privacy_global.php?s=masseriatorricella"></iframe>
<!-- fine banner policy cookie -->
    </body>
</HTML>