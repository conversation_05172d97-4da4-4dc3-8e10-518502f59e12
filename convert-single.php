<?php
/**
 * Script per convertire un singolo file HTML
 * Esempio di utilizzo del migration helper
 */

// Abilita il sistema di template
define('TEMPLATE_SYSTEM', true);

// <PERSON>ludi il migration helper
require_once 'includes/migration-helper.php';

// Esempio: Converti un file specifico
try {
    // Converti presentazione.htm in presentazione.php
    $output_file = MigrationHelper::convertHtmlToTemplate('presentazione.htm');
    echo "✅ Conversione completata!\n";
    echo "File originale: presentazione.htm\n";
    echo "File convertito: $output_file\n";
    
} catch (Exception $e) {
    echo "❌ Errore durante la conversione: " . $e->getMessage() . "\n";
}

// Altri esempi di conversione
$files_to_convert = [
    'contatti.htm',
    'masseria.htm', 
    'storia.htm',
    'camere.htm'
];

echo "\n=== Conversione multipla ===\n";
foreach ($files_to_convert as $file) {
    if (file_exists($file)) {
        try {
            $output = MigrationHelper::convertHtmlToTemplate($file);
            echo "✅ $file → $output\n";
        } catch (Exception $e) {
            echo "❌ Errore con $file: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️  File non trovato: $file\n";
    }
}
?>
