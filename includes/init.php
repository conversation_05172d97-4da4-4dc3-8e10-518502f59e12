<?php
/**
 * Template System Initialization
 * This file should be included at the top of every page
 */

// Define template system constant to prevent direct access to includes
define('TEMPLATE_SYSTEM', true);

// Start output buffering to allow headers to be sent later
ob_start();

// Include configuration
require_once __DIR__ . '/config.php';

// Helper function to render the complete page
function renderPage($content_callback, $page_config = []) {
    global $site_config, $languages, $navigation;
    
    // Extract page configuration
    extract($page_config);
    
    // Include header
    include __DIR__ . '/header.php';
    
    // Include navigation
    include __DIR__ . '/navigation.php';
    
    // Execute content callback
    if (is_callable($content_callback)) {
        $content_callback();
    } else {
        echo $content_callback;
    }
    
    // Include footer
    include __DIR__ . '/footer.php';
}

// Helper function to include a content template
function includeContent($template_file, $variables = []) {
    // Extract variables to make them available in the template
    extract($variables);
    
    // Include the template file
    include $template_file;
}

// Helper function to get localized text
function getText($key, $lang = null) {
    if ($lang === null) {
        $lang = getCurrentLanguage();
    }
    
    // This can be expanded to include a full translation system
    $translations = [
        'it' => [
            'welcome' => 'Benvenuti su',
            'read_more' => 'Leggi',
            'book_now' => 'Prenota ora',
            'contact_us' => 'Contattaci'
        ],
        'en' => [
            'welcome' => 'Welcome to',
            'read_more' => 'Read more',
            'book_now' => 'Book now', 
            'contact_us' => 'Contact us'
        ],
        'de' => [
            'welcome' => 'Willkommen bei',
            'read_more' => 'Mehr lesen',
            'book_now' => 'Jetzt buchen',
            'contact_us' => 'Kontaktieren Sie uns'
        ],
        'es' => [
            'welcome' => 'Bienvenidos a',
            'read_more' => 'Leer más',
            'book_now' => 'Reservar ahora',
            'contact_us' => 'Contáctanos'
        ],
        'fr' => [
            'welcome' => 'Bienvenue à',
            'read_more' => 'Lire la suite',
            'book_now' => 'Réserver maintenant',
            'contact_us' => 'Nous contacter'
        ]
    ];
    
    return isset($translations[$lang][$key]) ? $translations[$lang][$key] : $key;
}

?>
