<?php
/**
 * Masseria Torricella Template System Configuration
 * Centralized configuration for multilingual website
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

// Language configuration
$languages = [
    'it' => [
        'name' => 'Italiano',
        'code' => 'it',
        'flag' => 'ita',
        'dir' => '',
        'home' => 'index.html'
    ],
    'en' => [
        'name' => 'English', 
        'code' => 'en',
        'flag' => 'eng',
        'dir' => 'eng/',
        'home' => 'eng/index.html'
    ],
    'de' => [
        'name' => 'Deutsch',
        'code' => 'de', 
        'flag' => 'deu',
        'dir' => 'deu/',
        'home' => 'deu/index.html'
    ],
    'es' => [
        'name' => 'Español',
        'code' => 'es',
        'flag' => 'spa', 
        'dir' => 'spa/',
        'home' => 'spa/index.html'
    ],
    'fr' => [
        'name' => 'Français',
        'code' => 'fr',
        'flag' => 'fra',
        'dir' => 'fra/',
        'home' => 'fra/index.html'
    ]
];

// Site configuration
$site_config = [
    'title' => 'Agriturismo Masseria Torricella',
    'description' => 'Agriturismo Masseria Torricella, il luogo ideale dove trascorrere una vacanza in totale relax.',
    'keywords' => 'agriturismo puglia, AGRITURISMO PUGLIA, B&B Puglia, hotel puglia, HOTEL PUGLIA, ALBEROBELLO puglia, alberobello PUGLIA, masseria puglia, MASSERIA PUGLIA',
    'author' => 'Actiweb - www.acti-web.com',
    'contact_email' => '<EMAIL>',
    'pec_email' => '<EMAIL>',
    'phone' => '3333968173',
    'address' => 'S.P. Canale di Pirro, 19 70011 Alberobello (BA)',
    'vat' => '04140280720',
    'google_analytics' => 'G-XQ3WQSR0K6'
];

// Navigation menu structure (multilingual)
$navigation = [
    'it' => [
        'home' => ['url' => 'index.html', 'title' => 'Home'],
        'masseria' => [
            'title' => 'La masseria',
            'dropdown' => [
                'presentazione' => ['url' => 'presentazione.htm', 'title' => 'Presentazione'],
                'masseria' => ['url' => 'masseria.htm', 'title' => 'la masseria'],
                'storia' => ['url' => 'storia.htm', 'title' => 'Storia e origini'],
                'posizione' => ['url' => 'posizione.htm', 'title' => 'La posizione'],
                'dintorni' => ['url' => 'dintorni.htm', 'title' => 'Nei dintorni']
            ]
        ],
        'ospitalita' => [
            'title' => 'OSPITALITA\'',
            'dropdown' => [
                'camere' => ['url' => 'camere.htm', 'title' => 'Le camere'],
                'casevacanza' => ['url' => 'casevacanza.htm', 'title' => 'Le case vacanza'],
                'prenotazioni' => ['url' => 'prenotazioni.htm', 'title' => 'Richiesta Prenotazioni']
            ]
        ],
        'servizi' => [
            'title' => 'Cosa offriamo',
            'dropdown' => [
                'ristorante' => ['url' => 'ristorante.htm', 'title' => 'Il ristorante'],
                'spa' => ['url' => 'spa.htm', 'title' => 'La SPA'],
                'piscina' => ['url' => 'piscina.htm', 'title' => 'La piscina'],
                'servizi' => ['url' => 'servizi.htm', 'title' => 'Servizi'],
                'natura' => ['url' => 'natura.htm', 'title' => 'Area naturalistica']
            ]
        ],
        'cantina' => ['url' => 'https://www.cantinamasseriatorricella.it/', 'title' => 'La cantina'],
        'gallery' => ['url' => 'gallery.htm', 'title' => 'PHOTOGALLERY'],
        'contatti' => [
            'title' => 'CONTATTI',
            'dropdown' => [
                'info' => ['url' => 'contatti.htm', 'title' => 'Info'],
                'dove' => ['url' => 'dovesiamo.htm', 'title' => 'Dove siamo']
            ]
        ]
    ],
    'en' => [
        'home' => ['url' => 'index.html', 'title' => 'Home'],
        'masseria' => [
            'title' => 'THE FARMHOUSE',
            'dropdown' => [
                'presentazione' => ['url' => 'presentazione.htm', 'title' => 'OVERVIEW'],
                'masseria' => ['url' => 'masseria.htm', 'title' => 'THE FARMHOUSE'],
                'storia' => ['url' => 'storia.htm', 'title' => 'History and origins'],
                'posizione' => ['url' => 'posizione.htm', 'title' => 'The location'],
                'dintorni' => ['url' => 'dintorni.htm', 'title' => 'In the surroundings']
            ]
        ],
        'ospitalita' => [
            'title' => 'HOSPITALITY',
            'dropdown' => [
                'camere' => ['url' => 'camere.htm', 'title' => 'The rooms'],
                'casevacanza' => ['url' => 'casevacanza.htm', 'title' => 'Holiday homes'],
                'prenotazioni' => ['url' => 'prenotazioni.htm', 'title' => 'Booking request']
            ]
        ],
        'servizi' => [
            'title' => 'What we offer',
            'dropdown' => [
                'ristorante' => ['url' => 'ristorante.htm', 'title' => 'The restaurant'],
                'spa' => ['url' => 'spa.htm', 'title' => 'The SPA'],
                'piscina' => ['url' => 'piscina.htm', 'title' => 'The swimming pool'],
                'servizi' => ['url' => 'servizi.htm', 'title' => 'Services'],
                'natura' => ['url' => 'natura.htm', 'title' => 'Nature area']
            ]
        ],
        'cantina' => ['url' => 'https://www.cantinamasseriatorricella.it/', 'title' => 'The winery'],
        'gallery' => ['url' => 'gallery.htm', 'title' => 'PHOTOGALLERY'],
        'contatti' => [
            'title' => 'CONTACTS',
            'dropdown' => [
                'info' => ['url' => 'contatti.htm', 'title' => 'Info'],
                'dove' => ['url' => 'dovesiamo.htm', 'title' => 'Where we are']
            ]
        ]
    ]
    // Additional languages can be added here following the same pattern
];

// Helper functions
function getCurrentLanguage() {
    global $languages;
    
    // Detect language from URL path
    $path = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim($path, '/'));
    
    foreach ($languages as $code => $lang) {
        if ($lang['dir'] && strpos($path, $lang['dir']) === 1) {
            return $code;
        }
    }
    
    return 'it'; // Default to Italian
}

function getAssetPath($current_lang = 'it') {
    global $languages;
    return $languages[$current_lang]['dir'] . 'assets/';
}

function getLanguageUrl($target_lang, $current_page = 'index.html') {
    global $languages;
    
    if ($target_lang === 'it') {
        return '../' . $current_page;
    }
    
    return '../' . $languages[$target_lang]['dir'] . $current_page;
}

?>
