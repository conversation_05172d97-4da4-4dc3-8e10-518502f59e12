<?php

/**
 * Masseria Torricella Template System Configuration
 * Centralized configuration for multilingual website
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

// Language configuration
$languages = [
    'it' => [
        'name' => 'Italiano',
        'code' => 'it',
        'flag' => 'ita',
        'dir' => '',
        'home' => 'index.php'
    ],
    'en' => [
        'name' => 'English',
        'code' => 'en',
        'flag' => 'eng',
        'dir' => 'eng/',
        'home' => 'eng/index.php'
    ],
    'de' => [
        'name' => 'Deutsch',
        'code' => 'de',
        'flag' => 'deu',
        'dir' => 'deu/',
        'home' => 'deu/index.php'
    ],
    'es' => [
        'name' => 'Español',
        'code' => 'es',
        'flag' => 'spa',
        'dir' => 'spa/',
        'home' => 'spa/index.php'
    ],
    'fr' => [
        'name' => 'Français',
        'code' => 'fr',
        'flag' => 'fra',
        'dir' => 'fra/',
        'home' => 'fra/index.php'
    ]
];

// Site configuration
$site_config = [
    'title' => 'Agriturismo Masseria Torricella',
    'description' => 'Agriturismo Masseria Torricella, il luogo ideale dove trascorrere una vacanza in totale relax.',
    'keywords' => 'agriturismo puglia, AGRITURISMO PUGLIA, B&B Puglia, hotel puglia, HOTEL PUGLIA, ALBEROBELLO puglia, alberobello PUGLIA, masseria puglia, MASSERIA PUGLIA',
    'author' => 'Actiweb - www.acti-web.com',
    'contact_email' => '<EMAIL>',
    'pec_email' => '<EMAIL>',
    'phone' => '3333968173',
    'address' => 'S.P. Canale di Pirro, 19 70011 Alberobello (BA)',
    'vat' => '04140280720',
    'google_analytics' => 'G-XQ3WQSR0K6'
];

// Navigation menu structure (multilingual)
$navigation = [
    'it' => [
        'home' => ['url' => 'index.php', 'title' => 'Home'],
        'masseria' => [
            'title' => 'La masseria',
            'dropdown' => [
                'presentazione' => ['url' => 'presentazione.php', 'title' => 'Presentazione'],
                'masseria' => ['url' => 'masseria.php', 'title' => 'la masseria'],
                'storia' => ['url' => 'storia.php', 'title' => 'Storia e origini'],
                'posizione' => ['url' => 'posizione.php', 'title' => 'La posizione'],
                'dintorni' => ['url' => 'dintorni.php', 'title' => 'Nei dintorni']
            ]
        ],
        'ospitalita' => [
            'title' => 'OSPITALITA\'',
            'dropdown' => [
                'camere' => ['url' => 'camere.php', 'title' => 'Le camere'],
                'casevacanza' => ['url' => 'casevacanza.php', 'title' => 'Le case vacanza'],
                'prenotazioni' => ['url' => 'prenotazioni.php', 'title' => 'Richiesta Prenotazioni']
            ]
        ],
        'servizi' => [
            'title' => 'Cosa offriamo',
            'dropdown' => [
                'ristorante' => ['url' => 'ristorante.php', 'title' => 'Il ristorante'],
                'spa' => ['url' => 'spa.php', 'title' => 'La SPA'],
                'piscina' => ['url' => 'piscina.php', 'title' => 'La piscina'],
                'servizi' => ['url' => 'servizi.php', 'title' => 'Servizi'],
                'natura' => ['url' => 'natura.php', 'title' => 'Area naturalistica']
            ]
        ],
        'cantina' => ['url' => 'https://www.cantinamasseriatorricella.it/', 'title' => 'La cantina'],
        'gallery' => ['url' => 'gallery.php', 'title' => 'PHOTOGALLERY'],
        'contatti' => [
            'title' => 'CONTATTI',
            'dropdown' => [
                'info' => ['url' => 'contatti.php', 'title' => 'Info'],
                'dove' => ['url' => 'dovesiamo.php', 'title' => 'Dove siamo']
            ]
        ]
    ],
    'en' => [
        'home' => ['url' => 'index.php', 'title' => 'Home'],
        'masseria' => [
            'title' => 'THE FARMHOUSE',
            'dropdown' => [
                'presentazione' => ['url' => 'presentazione.php', 'title' => 'OVERVIEW'],
                'masseria' => ['url' => 'masseria.php', 'title' => 'THE FARMHOUSE'],
                'storia' => ['url' => 'storia.php', 'title' => 'History and origins'],
                'posizione' => ['url' => 'posizione.php', 'title' => 'The location'],
                'dintorni' => ['url' => 'dintorni.php', 'title' => 'In the surroundings']
            ]
        ],
        'ospitalita' => [
            'title' => 'HOSPITALITY',
            'dropdown' => [
                'camere' => ['url' => 'camere.php', 'title' => 'The rooms'],
                'casevacanza' => ['url' => 'casevacanza.php', 'title' => 'Holiday homes'],
                'prenotazioni' => ['url' => 'prenotazioni.php', 'title' => 'Booking request']
            ]
        ],
        'servizi' => [
            'title' => 'What we offer',
            'dropdown' => [
                'ristorante' => ['url' => 'ristorante.php', 'title' => 'The restaurant'],
                'spa' => ['url' => 'spa.php', 'title' => 'The SPA'],
                'piscina' => ['url' => 'piscina.php', 'title' => 'The swimming pool'],
                'servizi' => ['url' => 'servizi.php', 'title' => 'Services'],
                'natura' => ['url' => 'natura.php', 'title' => 'Nature area']
            ]
        ],
        'cantina' => ['url' => 'https://www.cantinamasseriatorricella.it/', 'title' => 'The winery'],
        'gallery' => ['url' => 'gallery.php', 'title' => 'PHOTOGALLERY'],
        'contatti' => [
            'title' => 'CONTACTS',
            'dropdown' => [
                'info' => ['url' => 'contatti.php', 'title' => 'Info'],
                'dove' => ['url' => 'dovesiamo.php', 'title' => 'Where we are']
            ]
        ]
    ]
    // Additional languages can be added here following the same pattern
];

// Helper functions
function getCurrentLanguage()
{
    global $languages;

    // Detect language from URL path
    $path = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim($path, '/'));

    foreach ($languages as $code => $lang) {
        if ($lang['dir'] && strpos($path, $lang['dir']) === 1) {
            return $code;
        }
    }

    return 'it'; // Default to Italian
}

function getAssetPath($current_lang = 'it')
{
    global $languages;
    //return $languages[$current_lang]['dir'] . 'assets/';
    return '/../assets/';
}

function getLanguageUrl($target_lang, $current_page = 'index.php')
{
    global $languages;

    if ($target_lang === 'it') {
        return '../' . $current_page;
    }

    return '../' . $languages[$target_lang]['dir'] . $current_page;
}
