<?php
/**
 * Footer Template for Masseria Torricella
 * Contains footer content, language switcher, and closing scripts
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

$current_lang = getCurrentLanguage();
$asset_path = getAssetPath($current_lang);

// Footer text translations
$footer_text = [
    'it' => [
        'contacts' => 'Contatti',
        'language' => 'Language:',
        'photogallery' => 'PHOTOGALLERY',
        'realization' => 'REALIZZAZIONE:'
    ],
    'en' => [
        'contacts' => 'Contacts',
        'language' => 'Language:',
        'photogallery' => 'PHOTOGALLERY', 
        'realization' => 'Realization:'
    ],
    'de' => [
        'contacts' => 'Kontakte',
        'language' => 'Sprache:',
        'photogallery' => 'PHOTOGALLERY',
        'realization' => 'Realisierung:'
    ],
    'es' => [
        'contacts' => 'Contactos',
        'language' => 'idioma:',
        'photogallery' => 'PHOTOGALLERY',
        'realization' => 'realización:'
    ],
    'fr' => [
        'contacts' => 'Contacts',
        'language' => 'Langue:',
        'photogallery' => 'PHOTOGALLERY',
        'realization' => 'Réalisation:'
    ]
];

$text = isset($footer_text[$current_lang]) ? $footer_text[$current_lang] : $footer_text['it'];
?>

<!-- Footer -->
<footer>
    <div class="container">
        <div class="row">
            <div class="widget span3">
                <h4><?php echo $text['contacts']; ?></h4>
                <p><i class="icon-map-marker"></i><?php echo htmlspecialchars($site_config['address']); ?></p>
                <p><i class="icon-phone"></i> Cell. <?php echo htmlspecialchars($site_config['phone']); ?></p>
                <p><i class="icon-envelope-alt"></i> Email: <a href="mailto:<?php echo $site_config['contact_email']; ?>"><?php echo $site_config['contact_email']; ?></a></p>
                <p><i class="icon-envelope-alt"></i> Email: <a href="mailto:<?php echo $site_config['pec_email']; ?>"><?php echo $site_config['pec_email']; ?></a></p>
                <p>P.IVA: <?php echo htmlspecialchars($site_config['vat']); ?></p>
                <p>
                <h4>&nbsp;</h4>
                <h4><?php echo $text['realization']; ?></h4>
                <a href="http://www.acti-web.com">Actiweb</a>
                </p>
            </div>
            <div class="widget span3">
                <h4><?php echo $text['language']; ?></h4>
                <div class="social span8">
                    <?php foreach ($languages as $code => $lang): ?>
                        <a class="<?php echo $lang['flag']; ?>" href="<?php echo getLanguageUrl($code, $current_page); ?>"></a>
                    <?php endforeach; ?>
                </div>
                <h4>&nbsp;</h4>
                
                <h4><?php echo $text['photogallery']; ?></h4>
                <div class="social span8">
                    <a class="gal1" href="gallery.htm"></a>
                    <a class="gal2" href="gallery2.htm"></a>
                    <a class="gal3" href="gallery3.htm"></a>
                </div>
                <h4>&nbsp;</h4>
            </div>
            <div class="widget span3">
                <iframe src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fmasseriatorricellaalberobello%2F&tabs=timeline&width=340&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId" width="340" height="500" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowTransparency="true"></iframe>
            </div>
            <div class="widget span3">
                <iframe src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fmasseriatorricellaviverebio%2F&tabs=timeline&width=340&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId" width="340" height="500" style="border:none;overflow:hidden;margin-left:45px" scrolling="no" frameborder="0" allowTransparency="true"></iframe>
            </div>
        </div>
        <div class="footer-border"></div>
        <div class="row"></div>
    </div>
</footer>

<!-- Javascript -->
<script src="<?php echo $asset_path; ?>js/jquery-1.8.2.min.js"></script>
<script src="<?php echo $asset_path; ?>bootstrap/js/bootstrap.min.js"></script>
<script src="<?php echo $asset_path; ?>js/jquery.flexslider.js"></script>
<script src="<?php echo $asset_path; ?>prettyPhoto/js/jquery.prettyPhoto.js"></script>
<script src="<?php echo $asset_path; ?>js/scripts.js"></script>

<!-- Cookie Policy Banner -->
<script type="text/javascript" src="https://www.acti-web.org/privacy-v2/privacy_script.php?s=MASSERIATORRICELLAIT"></script>

<?php if (isset($additional_footer_scripts)): ?>
<?php echo $additional_footer_scripts; ?>
<?php endif; ?>

</body>
</html>
