<?php
/**
 * Header Template for Masseria Torricella
 * Contains HTML head section and opening body tag
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

// Get current language and configuration
$current_lang = getCurrentLanguage();
$asset_path = getAssetPath($current_lang);

// Set default values if not provided
$page_title = isset($page_title) ? $page_title : $site_config['title'];
$page_description = isset($page_description) ? $page_description : $site_config['description'];
$page_keywords = isset($page_keywords) ? $page_keywords : $site_config['keywords'];
?>
<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>">

<head>
    <meta charset="utf-8">
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="Keywords" content="<?php echo htmlspecialchars($page_keywords); ?>" />
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="author" content="<?php echo htmlspecialchars($site_config['author']); ?>">

    <!-- CSS -->
    <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Open+Sans:400italic,400">
    <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Droid+Sans">
    <link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Lobster">
    <link rel="stylesheet" href="<?php echo $asset_path; ?>bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo $asset_path; ?>prettyPhoto/css/prettyPhoto.css">
    <link rel="stylesheet" href="<?php echo $asset_path; ?>css/flexslider.css">
    <link rel="stylesheet" href="<?php echo $asset_path; ?>css/font-awesome.css">
    <link rel="stylesheet" href="<?php echo $asset_path; ?>css/style.css">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
        <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <!-- Favicon and touch icons -->
    <link rel="shortcut icon" href="<?php echo $asset_path; ?>ico/favicon.ico">
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="<?php echo $asset_path; ?>ico/apple-touch-icon-144-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="<?php echo $asset_path; ?>ico/apple-touch-icon-114-precomposed.png">
    <link rel="apple-touch-icon-precomposed" sizes="72x72" href="<?php echo $asset_path; ?>ico/apple-touch-icon-72-precomposed.png">
    <link rel="apple-touch-icon-precomposed" href="<?php echo $asset_path; ?>ico/apple-touch-icon-57-precomposed.png">
    
    <style type="text/css">
    body,td,th {
        font-family: "Open Sans", Helvetica, Arial, sans-serif;
    }
    body {
        background-color: #FFFCEC;
        background-repeat: no-repeat;
    }
    </style>

    <?php if (isset($include_booking_widget) && $include_booking_widget): ?>
    <script src="https://booking.slope.it/js/widget.js" type="text/javascript"></script>
    <?php endif; ?>
    
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo $site_config['google_analytics']; ?>"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '<?php echo $site_config['google_analytics']; ?>');
    </script>
    
    <?php if (isset($additional_head_content)): ?>
    <?php echo $additional_head_content; ?>
    <?php endif; ?>
</head>

<body>
