<?php
/**
 * Navigation Template for Masseria Torricella
 * Generates the main navigation menu based on current language
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

$current_lang = getCurrentLanguage();
$current_page = isset($current_page) ? $current_page : 'index.html';
$nav_items = isset($navigation[$current_lang]) ? $navigation[$current_lang] : $navigation['it'];
?>

<!-- Header -->
<div class="container">
    <div class="header row">
        <div class="span12">
            <div class="navbar">
                <div class="navbar-inner">
                    <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </a>
                    <h1>
                        <a class="brand"></a>
                    </h1>
                    <div class="nav-collapse collapse">
                        <ul class="nav pull-right">
                            <?php foreach ($nav_items as $key => $item): ?>
                                <?php if (isset($item['dropdown'])): ?>
                                    <!-- Dropdown menu -->
                                    <li class="dropdown">
                                        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                            <?php echo htmlspecialchars($item['title']); ?><b class="caret"></b>
                                        </a>
                                        <ul class="dropdown-menu">
                                            <?php $first = true; ?>
                                            <?php foreach ($item['dropdown'] as $sub_key => $sub_item): ?>
                                                <?php if (!$first): ?>
                                                    <li class="divider"></li>
                                                <?php endif; ?>
                                                <li>
                                                    <a href="<?php echo htmlspecialchars($sub_item['url']); ?>">
                                                        <?php echo htmlspecialchars($sub_item['title']); ?>
                                                    </a>
                                                </li>
                                                <?php $first = false; ?>
                                            <?php endforeach; ?>
                                        </ul>
                                    </li>
                                <?php else: ?>
                                    <!-- Regular menu item -->
                                    <li<?php echo ($current_page === $item['url']) ? ' class="current-page"' : ''; ?>>
                                        <a href="<?php echo htmlspecialchars($item['url']); ?>">
                                            <?php echo htmlspecialchars($item['title']); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
