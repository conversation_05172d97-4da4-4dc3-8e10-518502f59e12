<?php
/**
 * Migration Helper Script
 * Helps convert existing HTML files to the new template system
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

class MigrationHelper {
    
    /**
     * Extract content between specific HTML tags
     */
    public static function extractContent($html, $start_marker, $end_marker) {
        $start_pos = strpos($html, $start_marker);
        if ($start_pos === false) return '';
        
        $start_pos += strlen($start_marker);
        $end_pos = strpos($html, $end_marker, $start_pos);
        if ($end_pos === false) return '';
        
        return trim(substr($html, $start_pos, $end_pos - $start_pos));
    }
    
    /**
     * Extract page title from HTML
     */
    public static function extractTitle($html) {
        if (preg_match('/<title>(.*?)<\/title>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }
    
    /**
     * Extract meta description
     */
    public static function extractDescription($html) {
        if (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }
    
    /**
     * Extract meta keywords
     */
    public static function extractKeywords($html) {
        if (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }
    
    /**
     * Extract main content (everything between navigation and footer)
     */
    public static function extractMainContent($html) {
        // Try to find content after navigation
        $content = self::extractContent($html, '</div>', '<!-- Footer -->');
        
        if (empty($content)) {
            // Fallback: extract content between body tags, excluding header/footer
            $content = self::extractContent($html, '<body>', '</body>');
            
            // Remove header section
            $content = preg_replace('/<div class="container">\s*<div class="header row">.*?<\/div>\s*<\/div>/s', '', $content);
            
            // Remove footer section
            $content = preg_replace('/<footer>.*?<\/footer>/s', '', $content);
            
            // Remove scripts
            $content = preg_replace('/<script.*?<\/script>/s', '', $content);
        }
        
        return trim($content);
    }
    
    /**
     * Generate PHP template from HTML file
     */
    public static function convertHtmlToTemplate($html_file, $output_file = null) {
        if (!file_exists($html_file)) {
            throw new Exception("HTML file not found: $html_file");
        }
        
        $html = file_get_contents($html_file);
        
        // Extract metadata
        $title = self::extractTitle($html);
        $description = self::extractDescription($html);
        $keywords = self::extractKeywords($html);
        $content = self::extractMainContent($html);
        
        // Determine output file
        if ($output_file === null) {
            $output_file = str_replace('.htm', '.php', $html_file);
            $output_file = str_replace('.html', '.php', $output_file);
        }
        
        // Generate PHP template
        $template = self::generatePhpTemplate($title, $description, $keywords, $content, basename($html_file));
        
        // Write to file
        file_put_contents($output_file, $template);
        
        return $output_file;
    }
    
    /**
     * Generate PHP template content
     */
    private static function generatePhpTemplate($title, $description, $keywords, $content, $filename) {
        $template = '<?php
/**
 * ' . ucfirst(str_replace(['.htm', '.html'], '', $filename)) . ' Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once \'includes/init.php\';

// Page configuration
$page_config = [
    \'page_title\' => \'' . addslashes($title) . '\',
    \'page_description\' => \'' . addslashes($description) . '\',
    \'page_keywords\' => \'' . addslashes($keywords) . '\',
    \'current_page\' => \'' . $filename . '\'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

' . $content . '

<?php
}

// Render the complete page
renderPage(\'renderContent\', $page_config);
?>';

        return $template;
    }
    
    /**
     * Batch convert multiple HTML files
     */
    public static function batchConvert($directory, $pattern = '*.htm') {
        $files = glob($directory . '/' . $pattern);
        $converted = [];
        
        foreach ($files as $file) {
            try {
                $output = self::convertHtmlToTemplate($file);
                $converted[] = $output;
                echo "Converted: $file -> $output\n";
            } catch (Exception $e) {
                echo "Error converting $file: " . $e->getMessage() . "\n";
            }
        }
        
        return $converted;
    }
}

// Example usage (uncomment to use):
/*
define('TEMPLATE_SYSTEM', true);
require_once 'config.php';

// Convert a single file
// MigrationHelper::convertHtmlToTemplate('presentazione.htm');

// Batch convert all .htm files in current directory
// MigrationHelper::batchConvert('.', '*.htm');
*/

?>
