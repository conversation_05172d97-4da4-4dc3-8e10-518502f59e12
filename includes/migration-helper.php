<?php

/**
 * Migration Helper Script
 * Helps convert existing HTML files to the new template system
 */

// Prevent direct access
if (!defined('TEMPLATE_SYSTEM')) {
    die('Direct access not permitted');
}

class MigrationHelper
{

    /**
     * Extract content between specific HTML tags
     */
    public static function extractContent($html, $start_marker, $end_marker)
    {
        $start_pos = strpos($html, $start_marker);
        if ($start_pos === false) return '';

        $start_pos += strlen($start_marker);
        $end_pos = strpos($html, $end_marker, $start_pos);
        if ($end_pos === false) return '';

        return trim(substr($html, $start_pos, $end_pos - $start_pos));
    }

    /**
     * Extract page title from HTML
     */
    public static function extractTitle($html)
    {
        if (preg_match('/<title>(.*?)<\/title>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }

    /**
     * Extract meta description
     */
    public static function extractDescription($html)
    {
        if (preg_match('/<meta\s+name=["\']description["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }

    /**
     * Extract meta keywords
     */
    public static function extractKeywords($html)
    {
        if (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }

    /**
     * Extract main content (everything between navigation and footer)
     */
    public static function extractMainContent($html, $output_file = null)
    {
        // Try multiple extraction strategies
        $content = '';

        // Strategy 1: Look for content after navigation div structure
        $nav_end_patterns = [
            '</div>\s*</div>\s*</div>\s*</div>',  // End of navigation container
            '</div>\s*</div>\s*</div>',           // Alternative navigation end
            '<div class="nav-collapse collapse">.*?</div>\s*</div>\s*</div>\s*</div>'
        ];

        foreach ($nav_end_patterns as $pattern) {
            // Escape the pattern and use different delimiters
            $escaped_pattern = preg_quote($pattern, '#');
            if (preg_match('#' . $escaped_pattern . '\s*(.*?)\s*<!-- Footer -->#s', $html, $matches)) {
                $content = $matches[1];
                break;
            }
        }

        // Strategy 2: If no footer comment, look for footer tag
        if (empty($content)) {
            foreach ($nav_end_patterns as $pattern) {
                $escaped_pattern = preg_quote($pattern, '#');
                if (preg_match('#' . $escaped_pattern . '\s*(.*?)\s*<footer>#s', $html, $matches)) {
                    $content = $matches[1];
                    break;
                }
            }
        }

        // Strategy 3: Fallback - extract everything between body tags and clean
        if (empty($content)) {
            $content = self::extractContent($html, '<body>', '</body>');

            // Remove header/navigation section
            $content = preg_replace('#<div class="container">\s*<div class="header row">.*?</div>\s*</div>\s*</div>#s', '', $content);

            // Remove footer section
            $content = preg_replace('#<footer>.*?</footer>#s', '', $content);

            // Remove scripts
            $content = preg_replace('#<script.*?</script>#s', '', $content);
        }

        // Clean up the extracted content
        $content = self::cleanExtractedContent($content);

        // Remove cookie policy banner
        $content = self::removeCookiePolicyBanner($content);

        // Convert internal links from .htm/.html to .php
        $content = self::convertInternalLinks($content);

        // Convert asset paths for files in subdirectories
        $content = self::convertAssetPaths($content, $output_file);

        return trim($content);
    }

    /**
     * Clean extracted content from unwanted tags and artifacts
     */
    private static function cleanExtractedContent($content)
    {
        // Remove HTML comments at the beginning (like <!-- Header -->)
        $content = preg_replace('#^\s*<!--[^>]*-->\s*#', '', $content);

        // Remove leading closing div tags (common artifact) - multiple passes
        do {
            $old_content = $content;
            $content = preg_replace('#^\s*(</div>\s*)+#', '', $content);
            $content = preg_replace('#^\s*</div>\s*#', '', $content);
        } while ($old_content !== $content);

        // Remove trailing opening div tags without content
        $content = preg_replace('#(<div[^>]*>\s*)+\s*$#', '', $content);

        // Remove empty div containers at the beginning
        $content = preg_replace('#^\s*<div[^>]*>\s*</div>\s*#', '', $content);

        // Remove standalone closing tags at the beginning (multiple types)
        $content = preg_replace('#^\s*</(div|span|p|h[1-6]|section|article|header|nav)[^>]*>\s*#', '', $content);

        // Remove empty lines with only whitespace at the beginning
        $content = preg_replace('#^\s*\n\s*\n#', '', $content);

        // Remove multiple consecutive empty lines
        $content = preg_replace('#\n\s*\n\s*\n#', "\n\n", $content);

        // Remove leading and trailing whitespace
        $content = trim($content);

        // Final cleanup: remove any remaining artifacts at the beginning
        $lines = explode("\n", $content);
        $cleaned_lines = [];
        $started = false;

        foreach ($lines as $line) {
            $trimmed = trim($line);

            // Skip empty lines and closing tags at the beginning
            if (!$started) {
                if (
                    empty($trimmed) ||
                    preg_match('#^</\w+>#', $trimmed) ||
                    preg_match('#^<!--.*-->$#', $trimmed)
                ) {
                    continue;
                }
                $started = true;
            }

            $cleaned_lines[] = $line;
        }

        return implode("\n", $cleaned_lines);
    }

    /**
     * Remove cookie policy banner from content
     */
    private static function removeCookiePolicyBanner($content)
    {
        // Remove the entire cookie policy banner block
        $patterns = [
            // Pattern 1: Complete banner with comments
            '#<!-- inizio banner policy cookie\s*-->.*?<!-- fine banner policy cookie\s*-->#s',

            // Pattern 2: Just the CSS link and iframe
            '#<link[^>]*acti-web\.com/privacy/stili\.css[^>]*>.*?<iframe[^>]*acti-web\.com/privacy/privacy_global\.php[^>]*></iframe>#s',

            // Pattern 3: Individual components
            '#<link[^>]*href=["\']http://www\.acti-web\.com/privacy/stili\.css["\'][^>]*>#',
            '#<iframe[^>]*src=["\']http://www\.acti-web\.com/privacy/privacy_global\.php[^"\']*["\'][^>]*></iframe>#',

            // Pattern 4: Comments only
            '#<!-- inizio banner policy cookie\s*-->#',
            '#<!-- fine banner policy cookie\s*-->#'
        ];

        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        // Clean up any extra empty lines left behind
        $content = preg_replace('#\n\s*\n\s*\n#', "\n\n", $content);

        return $content;
    }

    /**
     * Convert internal links from .htm/.html to .php
     */
    private static function convertInternalLinks($content)
    {
        // Pattern per trovare link interni con estensioni .htm o .html
        $patterns = [
            // href="pagina.htm"
            '#href=["\']([^"\']*?)\.htm(["\'])#i',
            // href="pagina.html"
            '#href=["\']([^"\']*?)\.html(["\'])#i',
            // href="directory/pagina.htm"
            '#href=["\']([^"\']*?/)([^/]*?)\.htm(["\'])#i',
            // href="directory/pagina.html"
            '#href=["\']([^"\']*?/)([^/]*?)\.html(["\'])#i'
        ];

        $replacements = [
            'href="$1.php$2',
            'href="$1.php$2',
            'href="$1$2.php$3',
            'href="$1$2.php$3'
        ];

        // Applica le sostituzioni
        $content = preg_replace($patterns, $replacements, $content);

        // Pattern più specifici per casi particolari
        $specific_patterns = [
            // action="form.htm" (per form)
            '#action=["\']([^"\']*?)\.htm(["\'])#i',
            '#action=["\']([^"\']*?)\.html(["\'])#i',

            // src="script.htm" (meno comune ma possibile)
            '#src=["\']([^"\']*?)\.htm(["\'])#i',
            '#src=["\']([^"\']*?)\.html(["\'])#i'
        ];

        $specific_replacements = [
            'action="$1.php$2',
            'action="$1.php$2',
            'src="$1.php$2',
            'src="$1.php$2'
        ];

        $content = preg_replace($specific_patterns, $specific_replacements, $content);

        // Escludi link esterni (che contengono http:// o https://)
        // Ripristina i link esterni che potrebbero essere stati modificati erroneamente
        $content = preg_replace('#(https?://[^"\']*?)\.php#i', '$1.html', $content);

        return $content;
    }

    /**
     * Convert asset paths for files in subdirectories
     */
    private static function convertAssetPaths($content, $output_file = null)
    {
        if (!$output_file) {
            return $content;
        }

        // Get the directory of the output file
        $output_dir = dirname($output_file);

        // If the file is not in a subdirectory, no conversion needed
        if ($output_dir === '.' || empty($output_dir)) {
            return $content;
        }

        // Count directory levels to determine how many ../ we need
        $dir_parts = array_filter(explode('/', $output_dir));
        $dir_levels = count($dir_parts);

        if ($dir_levels === 0) {
            return $content;
        }

        // Create the prefix for going back to root
        $back_to_root = str_repeat('../', $dir_levels);

        // Patterns for asset paths that need to be converted
        $asset_patterns = [
            // src="assets/..." -> src="../assets/..."
            '#src=["\'](?!https?://|//|\.\./|/)([^"\']*?)(["\'])#i',
            // href="assets/..." -> href="../assets/..." (for all files, not just CSS)
            '#href=["\'](?!https?://|//|mailto:|tel:|\.\./|/)([^"\']*?)(["\'])#i',
            // Background images in style attributes
            '#url\(["\']?(?!https?://|//|\.\./|/)([^"\']*?)\)#i',
            // Action attributes for forms
            '#action=["\'](?!https?://|//|\.\./|/)([^"\']*?)(["\'])#i'
        ];

        foreach ($asset_patterns as $pattern) {
            $content = preg_replace_callback($pattern, function ($matches) use ($back_to_root) {
                $path = $matches[1];
                $quote = isset($matches[2]) ? $matches[2] : '';

                // Skip if path already starts with ../ or is absolute or empty
                if (empty($path) || strpos($path, '../') === 0 || strpos($path, '/') === 0) {
                    return $matches[0];
                }

                // Skip if it's a PHP variable or template syntax
                if (strpos($path, '$') !== false || strpos($path, '<?') !== false) {
                    return $matches[0];
                }

                // Skip if it's not a relative path that needs conversion
                if (strpos($path, 'http') === 0 || strpos($path, 'mailto:') === 0 || strpos($path, 'tel:') === 0) {
                    return $matches[0];
                }

                // Add the back-to-root prefix
                $new_path = $back_to_root . $path;

                // Reconstruct the attribute
                if (strpos($matches[0], 'url(') !== false) {
                    return 'url(' . $new_path . ')';
                } else {
                    $attr_name = substr($matches[0], 0, strpos($matches[0], '=') + 1);
                    return $attr_name . '"' . $new_path . '"';
                }
            }, $content);
        }

        return $content;
    }

    /**
     * Generate PHP template from HTML file
     */
    public static function convertHtmlToTemplate($html_file, $output_file = null)
    {
        if (!file_exists($html_file)) {
            throw new Exception("HTML file not found: $html_file");
        }

        $html = file_get_contents($html_file);

        // Extract metadata
        $title = self::extractTitle($html);
        $description = self::extractDescription($html);
        $keywords = self::extractKeywords($html);
        // Determine output file first
        if ($output_file === null) {
            $output_file = str_replace('.htm', '.php', $html_file);
            $output_file = str_replace('.html', '.php', $output_file);
        }

        $content = self::extractMainContent($html, $output_file);

        // Generate PHP template
        $template = self::generatePhpTemplate($title, $description, $keywords, $content, basename($html_file), $output_file);

        // Write to file
        file_put_contents($output_file, $template);

        return $output_file;
    }

    /**
     * Generate PHP template content
     */
    private static function generatePhpTemplate($title, $description, $keywords, $content, $filename, $output_file = null)
    {
        // Calculate the correct path to includes/init.php based on output file location
        $includes_path = 'includes/init.php';

        if ($output_file) {
            // Get the directory of the output file
            $output_dir = dirname($output_file);

            // If the file is in a subdirectory, calculate relative path
            if ($output_dir !== '.' && !empty($output_dir)) {
                // Count directory levels (split by / and filter empty)
                $dir_parts = array_filter(explode('/', $output_dir));
                $dir_levels = count($dir_parts);

                if ($dir_levels > 0) {
                    $includes_path = str_repeat('../', $dir_levels) . 'includes/init.php';
                }
            }
        }

        $template = '<?php
/**
 * ' . ucfirst(str_replace(['.htm', '.html'], '', $filename)) . ' Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once \'' . $includes_path . '\';

// Page configuration
$page_config = [
    \'page_title\' => \'' . addslashes($title) . '\',
    \'page_description\' => \'' . addslashes($description) . '\',
    \'page_keywords\' => \'' . addslashes($keywords) . '\',
    \'current_page\' => \'' . $filename . '\'
];

// Content function
function renderContent() {
    $current_lang = getCurrentLanguage();
    $asset_path = getAssetPath($current_lang);
?>

' . $content . '

<?php
}

// Render the complete page
renderPage(\'renderContent\', $page_config);
?>';

        return $template;
    }

    /**
     * Batch convert multiple HTML files
     */
    public static function batchConvert($directory, $pattern = '*.htm')
    {
        $files = glob($directory . '/' . $pattern);
        $converted = [];

        foreach ($files as $file) {
            try {
                $output = self::convertHtmlToTemplate($file);
                $converted[] = $output;
                echo "Converted: $file -> $output\n";
            } catch (Exception $e) {
                echo "Error converting $file: " . $e->getMessage() . "\n";
            }
        }

        return $converted;
    }
}

// Example usage (uncomment to use):
/*
define('TEMPLATE_SYSTEM', true);
require_once 'config.php';

// Convert a single file
// MigrationHelper::convertHtmlToTemplate('presentazione.htm');

// Batch convert all .htm files in current directory
// MigrationHelper::batchConvert('.', '*.htm');
*/
